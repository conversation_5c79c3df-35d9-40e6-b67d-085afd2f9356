# -*-coding:utf-8-*-
import os


import cv2
print(cv2.cuda.getCudaEnabledDeviceCount())
print(cv2.__version__)

import tensorrt as trt
import pycuda.driver as drv
drv.init()
print(trt.__version__)
assert trt.Builder(trt.Logger())              # 验证是否安装成功
print('CUDA device count:', drv.Device.count())


import torch
print(torch.__version__)
print(torch.cuda.is_available())
try:
    print(f"cuDNN version: {torch.backends.cudnn.version()}")
except Exception as e:
    print(f"Could not retrieve cuDNN version: {e}, because {os.environ['LD_LIBRARY_PATH']}")
    print(f"临时解决方案: export LD_LIBRARY_PATH={os.path.dirname(torch.__file__)}/lib:$LD_LIBRARY_PATH ")
    print(f"详见：")

import onnxruntime as ort
print(ort.__version__)
print(ort.get_available_providers())

import matplotlib
import matplotlib.pyplot as plt
print(matplotlib.__version__)

import sklearn
print(sklearn.__version__)

from PIL import Image
print(Image.__version__)
import numpy as np
print(np.__version__)

import transformers
print(transformers.__version__)


try:
    import nncf  # 导入NNCF库
    # 基础功能 的兼容性测试
    from nncf import NNCFConfig
    from openvino.tools import mo
    from openvino.runtime import serialize
    print("✅ NNCF 与 PyTorch 兼容性建议 有效匹配版本: torch2.1.0 nncf 2.8.1 ")
except Exception as e:
    print(f"PyTorch Ver: {torch.__version__}")
    print(f"❌ 兼容性问题: {e}")

print('QAT: NNCF Liberary Version', nncf.__version__)


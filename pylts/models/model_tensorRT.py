# -*-coding:utf-8-*-
"""
@Moss created 2024/08/21
加载tensorRT的engine模型, 构建推理类
Ref1: https://github.com/HeKun-NVIDIA/TensorRT-Developer_Guide_in_Chinese/blob/main
Ref2：Install step;
tar -zxvf TensorRT-7.0.0.11.CentOS-7.6.x86_64-gnu.cuda-9.0.cudnn7.6.tar.gz
sudo vim ~/.bashrc  # 添加下面路径，注意改成自己的tensorRT的lib路径,cuda的路径
export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/home/<USER>/A/TensorRT-7.0.0.11/lib
export C_INCLUDE_PATH=/usr/local/cuda-9.0/include/:${C_INCLUDE_PATH}
export CPLUS_INCLUDE_PATH=/usr/local/cuda-9.0/include/:${CPLUS_INCLUDE_PATH}
source ~/.bashrc    # 使其生效

Q: onnx导出engine模型 对 onnx版本有要求么， engine输出全为0怎么办?
A: 可能的本质原因：（1）Gather的参数中的取最后一个维度数据用的是自动推断的-1，可能是算子不支持，需改成指定的维度；
                 （2）辅助头中数据很小超出了float16的表示范围，影响整体使用float16量化效果。
参考 https://blog.csdn.net/zhangqian_1/article/details/135477396 【未验证】

Q:性能优化：
* 如果需要频繁进行推理，可以考虑使用异步数据传输和流（stream）来提高性能，减少数据传输的等待时间.
* 如果模型只有一个输入输出，可以直接使用TensorRT的Python API进行推理，而不需要手动管理内存分配和数据传输。

"""

import time
from functools import partial
import numpy as np
from collections import OrderedDict, namedtuple

import tensorrt as trt
# assert trt.Builder(trt.Logger())              # 验证是否安装成功
import pycuda.driver as cuda
import pycuda.autoinit  # 初始化CUDA


import onnx
import onnxruntime


class mod_timer:
    """
    This is a Decorator of Timer module for func and code part:
    Usage:
    way1. @mod_timer
          def your_func():
            pass
    way2. with mod_timer() as t:
            your codes
    """

    def __init__(self, func=None):
        self.func = func
        self.start_code = None  # 片段
        self.end_code = None  # 片段

    def __call__(self, *args, **kwargs):
        _start = time.time()
        result = self.func(*args, **kwargs)
        _end = time.time()
        print(f"{self.func.__name__} run {_end - _start:.4f} s.")

        return result

    def __get__(self, obj):
        return partial(self.__call__, obj)

    def __enter__(self):
        self.start_code = time.time()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.end_code = time.time()
        print(f"This part run {self.end_code - self.start_code:.4f} s.")

    def __repr__(self):
        return f"<Mod_timer used for {self.func.__name__}>"


class ModelLoader_RT:
    # 适用于tensorRT 8.x, 10.x
    def __init__(self, model_path: str):
        self.logger = trt.Logger(trt.Logger.INFO)
        self.builder = trt.Builder(self.logger)
        self.config = self.builder.create_builder_config()

        self.config.set_flag(trt.BuilderFlag.FP16)          # 启用FP16模式, 这一步很重要，否则导出的模型size会翻倍

        if model_path.endswith('.onnx'):
            print(f"We will use {model_path} and export Engine model..")
            self.onnx2engine(model_path)

        assert model_path.endswith('.engine'), f"@Moss:{model_path} not a *.engine file."

        with open(model_path, "rb") as f, trt.Runtime(self.logger) as runtime:  # 使用Runtime接口反序列化引擎, 运行时需要记录器的实例
            self.engine = runtime.deserialize_cuda_engine(f.read())                 # 从内存缓冲区反序列化引擎

        self.context = self.engine.create_execution_context()


    def predict_v8(self, input_data: np.ndarray):
        # 创建数据
        self.bindings = OrderedDict()
        Binding = namedtuple('Binding', ('name', 'dtype', 'shape', 'data', 'ptr'))
        self.input_names, self.output_names = [], []
        dynamic, fp16 = False, False
        for i in range(self.engine.num_bindings):
            name = self.engine.get_tensor_name(i)  # 获得输入输出的名字"images","output0"
            shape = tuple(self.context.get_tensor_shape(name))
            dtype = self.engine.get_tensor_dtype(name)
            np_dtype = np.float16 if dtype == trt.DataType.FLOAT and fp16 else np.float32
            if self.engine.get_tensor_mode(name) == trt.TensorIOMode.INPUT:  # 判断是否为输入
                if -1 in tuple(self.engine.get_tensor_shape(name)):
                    dynamic = True
                    self.context.set_binding_shape(i, tuple(self.engine.get_profile_shape(0, i)[2]))
                fp16 = True if dtype == trt.DataType.HALF else False  # HALF为float16
                self.input_names.append(name)
            else:
                self.output_names.append(name)

            im = np.empty(shape, dtype=np_dtype)
            device_pointer = cuda.mem_alloc(int(trt.volume(shape) * np_dtype().itemsize))
            cuda.memcpy_htod(device_pointer, im.flatten())

            self.bindings[name] = Binding(name, dtype, shape, im, device_pointer)  # 放入之前创建的对象

        if len(self.input_names) == 1:
            self.input_name = self.input_names[0]
        if len(self.output_names) == 1:
            self.output_name = self.output_names[0]

        self.binding_addrs = OrderedDict((n, int(d.ptr)) for n, d in self.bindings.items())  # 提取name以及对应的Binding
        input_name = 'x'
        self.batch_size = self.bindings[input_name].shape[0]  # if dynamic, this is instead max batch size

        assert input_data.shape == self.bindings[self.input_name].shape, \
            f"input size {input_data.shape} != max_model_size"
        # 输出准备
        output_data = self.bindings[self.output_name].data
        # 输入准备
        device_pointer = cuda.mem_alloc(int(np.prod(input_data.shape) * input_data.dtype.itemsize))
        self.binding_addrs[self.input_name] = int(device_pointer)           # 修改输入路径
        device_pointer_out = self.bindings[self.output_name].ptr

        stream = cuda.Stream()
        # 如果使用优化配置文件，可以通过以下方式设置
        self.context.set_optimization_profile_async(0, stream.handle)

        binding_list = list(self.binding_addrs.values())            # print(list(self.binding_addrs.values()))
        # 推理
        cuda.memcpy_htod_async(device_pointer, input_data.flatten(), stream)

        for i in range(50):
            with mod_timer():
                # self.context.execute_async_v2(bindings=binding_list, stream_handle=stream.handle)     # 调用计算核心执行计算过程 return True
                self.context.execute_v2(bindings=binding_list)     # 调用计算核心执行计算过程 return True
        cuda.memcpy_dtoh_async(output_data, device_pointer_out, stream)

        # 同步 CUDA 流，确保推理完成
        stream.synchronize()

        outputs = [self.bindings[x].data for x in sorted(self.output_names)]

        return outputs[0] if len(outputs) == 1 else outputs

    @staticmethod
    def predict_v10pure(model_path, input_data: np.ndarray):
        '''单输入输出，简化推理过程'''
        logger = trt.Logger(trt.Logger.INFO)

        runtime = trt.Runtime(logger)
        with open(model_path, 'rb') as f:
            engine = runtime.deserialize_cuda_engine(f.read())

        # 使用engine创建执行上下文context，这是执行推理所必需的
        with engine.create_execution_context() as context:
            output_name = engine.get_tensor_name(1)                 # 获取输出的形状
            output_shape = context.get_tensor_shape(output_name)

            # 创建设备内存
            inputD0 = cuda.mem_alloc(input_data.nbytes)
            outputD0 = cuda.mem_alloc(trt.volume(output_shape) * input_data.dtype.itemsize)

            # 将数据从主机复制到设备
            cuda.memcpy_htod(inputD0, input_data)

            # 执行推理 | 循环测试推理时间
            # for i in range(50):
            with mod_timer():
                context.execute_v2(bindings=[int(inputD0), int(outputD0)])

            # 将结果从设备复制回主机
            outputH0 = np.empty(output_shape, dtype=np.float32)
            cuda.memcpy_dtoh(outputH0, outputD0)

            # 释放资源
            inputD0.free()
            outputD0.free()

        return outputH0

    @staticmethod
    def get_input_shapes(model_path):
        model = onnx.load(model_path)

        # 假设模型中包含了自定义属性，定义了动态范围
        min_shape = (1, 3, 224, 224)
        opt_shape = (4, 3, 224, 224)
        max_shape = (8, 3, 224, 224)

        # 如果模型中定义了动态范围，从模型中读取
        for attr in model.graph.input[0].type.tensor_type.shape.dim:
            if attr.HasField("denotation"):
                if attr.denotation == "min_shape":
                    min_shape = tuple(attr.dim_value)
                elif attr.denotation == "opt_shape":
                    opt_shape = tuple(attr.dim_value)
                elif attr.denotation == "max_shape":
                    max_shape = tuple(attr.dim_value)

        return min_shape, opt_shape, max_shape

    def onnx2engine(self, model_path: str):
        # parser
        network = self.builder.create_network(1 << int(trt.NetworkDefinitionCreationFlag.EXPLICIT_BATCH))
        parser = trt.OnnxParser(network, self.logger)
        success = parser.parse_from_file(model_path)            # onnx
        if not success:
            for idx in range(parser.num_errors):
                print(f"Error{idx}: {parser.get_error(idx)}")
            raise ValueError(f"@Moss: Failed to parse ONNX file.")
        # config = self.builder.create_builder_config()

        # 检测输入是否为动态维度
        input_tensor = network.get_input(0)
        is_dynamic = any(dim.is_dynamic for dim in input_tensor.shape if hasattr(dim, 'is_dynamic'))  # 检查是否有动态维度
        print(f"Your onnx module is_dynamic: {is_dynamic}")

        min_shape, opt_shape, max_shape = ModelLoader_RT.get_input_shapes(model_path)
        if is_dynamic:
            profile = self.builder.create_optimization_profile()
            input_name = input_tensor.name
            profile.set_shape_input(input_name, min_shape, opt_shape, max_shape)
            self.config.add_optimization_profile(profile)


        self.config.set_memory_pool_limit(trt.MemoryPoolType.WORKSPACE, 1 << 31)  # 1 MiB
        # 构建engin部分很耗时
        engine = self.builder.build_serialized_network(network, self.config)  # 直接构建引擎，不序列化到文件

        with open(model_path.replace('.onnx', '.engine'), "wb") as fw:
            fw.write(engine)

        # self.logger.INFO(f'导出成功！！ and exit')      # 需要自定义logger: MyLogger_RT()
        print(f'导出成功！！ and exit')
        exit()

    @staticmethod
    def predict_v10(model_path, input_data: np.ndarray):
        logger = trt.Logger(trt.Logger.INFO)

        runtime = trt.Runtime(logger)
        with open(model_path, 'rb') as f:
            engine = runtime.deserialize_cuda_engine(f.read())

        # 使用engine创建执行上下文context，这是执行推理所必需的
        with engine.create_execution_context() as context:
            # 获取输出的形状
            output_name = engine.get_tensor_name(1)
            output_shape = context.get_tensor_shape(output_name)

            # 创建CUDA流
            stream = cuda.Stream()

            # 创建设备内存
            inputD0 = cuda.mem_alloc(input_data.nbytes)
            outputD0 = cuda.mem_alloc(trt.volume(output_shape) * input_data.dtype.itemsize)

            # 将数据从主机复制到设备
            cuda.memcpy_htod_async(inputD0, input_data, stream)

            # 执行推理
            # for i in range(50):       循环测试推理时间
            with mod_timer():
                context.execute_v2(bindings=[int(inputD0), int(outputD0)])

            # 将结果从设备复制回主机
            outputH0 = np.empty(output_shape, dtype=np.float32)
            cuda.memcpy_dtoh_async(outputH0, outputD0, stream)

            # 同步流以确保所有操作完成
            stream.synchronize()

            # 释放资源
            inputD0.free()
            outputD0.free()



        return outputH0



class ModelLoader(ModelLoader_RT):
    def __init__(self, model_path):
        if model_path.endswith('.onnx'):
            self.session = onnxruntime.InferenceSession(model_path,
                                                        providers=['CUDAExecutionProvider'])  # providers：表示推理发生在对应的设备上
            assert len(self.session.get_providers()) ==2, f"@Moss: {self.session.get_providers()} may use CPU Infer,please Check it!"
        else:
            super(ModelLoader, self).__init__(model_path)

    def predict(self, input_data):
        input_name = self.session.get_inputs()[0].name
        output_name = self.session.get_outputs()[0].name
        result = torch.tensor(np.array(self.session.run([output_name], {input_name: input_data})))
        return result


# 备用
def modelLoader_RT(model_path):
    image_data = np.load('/home/<USER>/下载/predata.npy')

    logger = trt.Logger(trt.Logger.INFO)

    runtime = trt.Runtime(logger)
    with open(model_path, 'rb') as f:
        engine = runtime.deserialize_cuda_engine(f.read())

    # 使用engine创建执行上下文context，这是执行推理所必需的
    with engine.create_execution_context() as context:
        # 初始化列表bindings，绑定输入和输出的CUDA内存地址，循环遍历engine的所有绑定
        bindings = []
        for binding in engine:
            # binding是模型的input和output节点名，本例是input.1和437
            # 获取当前绑定的索引
            binding_idx = engine.get_binding_index(binding)
            # 获取当前绑定的形状
            size = trt.volume(context.get_binding_shape(binding_idx))
            # 获取当前绑定的数据类型
            dtype = engine.get_binding_dtype(binding)
            np_dtype = np.float32 if dtype == trt.DataType.FLOAT else np.float16
            # 若绑定是input
            if engine.binding_is_input(binding):
                # 分配input数据的CPU锁页内存和GPU显存
                input_buffer = np.ascontiguousarray(image_data)
                # 分配输入数据的cuda显存
                input_memory = cuda.mem_alloc(image_data.nbytes)
                # 分配的地址添加到bindings列表
                bindings.append(int(input_memory))
            # 若绑定是output
            else:
                # 分配output数据的CPU锁页内存和GPU显存
                output_buffer = cuda.pagelocked_empty(size, np_dtype)
                # 分配输出数据的cuda显存
                output_memory = cuda.mem_alloc(output_buffer.nbytes)
                # 分配的地址添加到bindings列表
                bindings.append(int(output_memory))
        # 创建cuda流
        stream = cuda.Stream()
        # 将输入数据转入cuda
        cuda.memcpy_htod_async(input_memory, input_buffer, stream)
        # 执行推理
        t1 = time.time()
        context.execute_async_v2(bindings=bindings, stream_handle=stream.handle)
        t2 =time.time()
        print(t2-t1)
        # 从GPU中将输出数据取出（output_buffer）
        cuda.memcpy_dtoh_async(output_buffer, output_memory, stream)
        # 同步流
        stream.synchronize()
    # 输出buffer长度是(n*c*h*w),需要reshape
    # output = np.reshape(output_buffer, (1, 2, 584, 565))


    return


# tensorRt10.x
def modelLoader_RT10x(model_path, input_data):

    logger = trt.Logger(trt.Logger.INFO)

    runtime = trt.Runtime(logger)
    with open(model_path, 'rb') as f:
        engine = runtime.deserialize_cuda_engine(f.read())

    # 使用engine创建执行上下文context，这是执行推理所必需的
    with engine.create_execution_context() as context:
        # 获取输出的形状
        output_name = engine.get_tensor_name(1)
        output_shape = context.get_tensor_shape(output_name)

        # 创建CUDA流
        stream = cuda.Stream()

        # 创建设备内存
        inputD0 = cuda.mem_alloc(input_data.nbytes)
        outputD0 = cuda.mem_alloc(trt.volume(output_shape) * input_data.dtype.itemsize)

        # 将数据从主机复制到设备
        cuda.memcpy_htod_async(inputD0, input_data, stream)

        # 执行推理
        # for i in range(50):       循环测试推理时间
        with mod_timer():
            context.execute_v2(bindings=[int(inputD0), int(outputD0)])

        # 将结果从设备复制回主机
        outputH0 = np.empty(output_shape, dtype=np.float32)
        cuda.memcpy_dtoh_async(outputH0, outputD0, stream)

        # 同步流以确保所有操作完成
        stream.synchronize()

        # 打印输出结果
        print("Output:", outputH0)

        # 释放资源
        inputD0.free()
        outputD0.free()

    return outputH0



if __name__ == '__main__':
    engine = ModelLoader_RT(model_path='/dockerSpace/stand_jumpt_detect_foot_20241123_v35_736_416_addpost.onnx')

    input_data = np.random.rand(1, 736, 416).astype(np.float32)
    # input_data = np.load('../example_data/predata.npy')

    print(trt.__version__)
    version_start = int(trt.__version__.split('.')[0])
    if version_start == 10:
        outputH0 = ModelLoader_RT.predict_v10(model_path='/dockerSpace/best_model.engine',
                                              input_data=input_data)
        print("Output:", outputH0)

    elif version_start == 8:
        engine = ModelLoader_RT(model_path='/dockerSpace/best_model.engine')
        output = engine.predict_v8(input_data)
        print(output)






"""
created @Moss 20250620
基于 torch模型的量化感知训练(QAT) 环境、流程测试:
测试通过: torch2.1.0+nncf2.8.1+openvidno-dev2023.3.1
输出:
    量化后模型 quantized_model.xml 和 quantized_model.bin         # 代码执行完成后自动删除

"""
import os

import torch
import torch.nn as nn
import nncf
from openvino.tools import mo
from openvino.runtime import serialize

import warnings
import traceback
from torch.jit import TracerWarning

# 抑制一些已知的警告
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=TracerWarning)


class SimpleModel(nn.Module):
    def __init__(self):
        super().__init__()
        self.fc = nn.Linear(10, 1)

    def forward(self, x):
        return self.fc(x)


def check_nncf_environment():
    """诊断NNCF环境"""
    print("=== NNCF环境诊断[已验证torch2.1.0+nncf2.8.1有效] ===")
    print(f"PyTorch版本: {torch.__version__}")
    print(f"NNCF版本: {nncf.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA版本: {torch.version.cuda}")
        print(f"cuDNN版本: {torch.backends.cudnn.version()}")
    print("=" * 30)



def transform_fn(data_item):
    # 确保模型的输入总是一个可迭代的参数列表
    return data_item,       # <--- 核心改动！返回 (tensor,) not tensor


def ultimate_quantization_test(model_fp32):
    """
    终极解决方案：强制将数据包装在元组中，以适应NNCF图追踪器的严格要求。
    """
    # --- 2. 准备校准数据 ---
    calibration_data = [torch.randn(1, 10) for _ in range(10)]
    calibration_dataset = nncf.Dataset(calibration_data, transform_fn)
    # print("✅ 2. 校准数据准备完毕（输入已包装为元组）")

    # --- 3. 执行量化 ---
    try:
        quantized_model = nncf.quantize(
            model=model_fp32,
            calibration_dataset=calibration_dataset,
            preset=nncf.QuantizationPreset.PERFORMANCE,
            subset_size=10
        )
        print(" ✅ 量化成功！")

        # --- 4. 验证和转换 ---
        test_input = torch.randn(1, 10)
        with torch.no_grad():
            original_out = model(test_input)
            quantized_out = quantized_model(test_input)
            diff = torch.abs(original_out - quantized_out).mean().item()
            print(f"✅ 验证通过，模型差异: {diff:.6f}")

        ov_model = mo.convert_model(quantized_model, example_input=test_input)
        serialize(ov_model, xml_path="quantized_model.xml")
        print(f"✅ OpenVINO 模型已保存: quantized_model.xml")

        print("\n 完整流程 successed!")

        os.remove('quantized_model.xml')
        os.remove('quantized_model.bin')
        print("✅  验证结束：已删除量化后模型 quantized_model.xml 和 quantized_model.bin")
        return True

    except Exception as e:
        print(f"❌ 量化流程再次失败，这表明该NNCF版本存在无法绕过的Bug。")
        print(f"  - 异常类型 (Type): {type(e)}")
        print(f"  - 异常信息 (Message): {e}")
        print("  - 详细追溯 (Traceback):")
        traceback.print_exc()
        return False


if __name__ == "__main__":
    check_nncf_environment()

    # --- 1. 创建模型 ---
    model = SimpleModel()
    model.eval()

    success = ultimate_quantization_test(model)
    if not success:
        print("\n" + "=" * 50)
        print("最后的建议：升级到最新的库版本")
        print("=" * 50)
        print("我们已经尝试了所有针对 NNCF 2.7.0 的代码技巧，但问题依旧。")
        print("这强烈表明您遇到了一个在 NNCF 2.7.0 中无法通过代码绕过的Bug。")
        print("唯一的解决方案是升级到最新的、已经修复了这些早期问题的版本。")
        print("\n推荐的安装命令：")
        print("1. pip uninstall nncf openvino-dev openvino torch torchvision torchaudio -y")
        print("2. pip install openvino-dev==2024.1.0")
        print("3. pip install torch==2.3.0 torchvision==0.18.0 --index-url https://download.pytorch.org/whl/cu118")
        print("\n升级后，您将拥有一个更稳定、功能更强大的环境。")

# -*-coding:utf-8-*-
import torch
import torch.nn as nn

"""
@classmethods 类方法使类模板具有记忆力
# 类方法处理的变量一定是类变量
把模型封装成类，把初始化网络和加载模型用类方法描述，这样1次load后，可一直使用模型
"""


class MyModel:
    def __init__(self):
        self.network = None

    @classmethod
    def init_network(cls):
        # 初始化网络的代码
        network = ...
        model = cls()
        model.network = network
        return model

    @classmethod
    def load_model(cls, model_path):
        # 加载模型的代码
        model = cls()
        model.network = ...
        return model

    def predict(self, x):
        # 使用模型进行预测的代码
        y_pred = ...
        return y_pred

# 使用示例
my_model = MyModel.init_network()
my_model = MyModel.load_model('path/to/model')
y_pred = my_model.predict(x)



# ---------------------------------------

class MyModel(nn.Module):
    def __init__(self, model_path=None):
        super(MyModel, self).__init__()
        # 初始化网络
        self.network = nn.Sequential(
            nn.Linear(784, 256),
            nn.ReLU(),
            nn.Linear(256, 10)
        )
        # 如果提供了模型路径，则加载模型
        if model_path is not None:
            self.load_model(model_path)

    def forward(self, x):
        # 前向传播
        x = self.network(x)
        return x

    def load_model(self, model_path):
        # 加载模型
        self.load_state_dict(torch.load(model_path))

    def predict(self, x):
        # 预测
        with torch.no_grad():
            y_pred = self.forward(x)
            y_pred = torch.argmax(y_pred, dim=1)
        return y_pred

# 使用示例
my_model = MyModel('path/to/model')
y_pred = my_model.predict(x)
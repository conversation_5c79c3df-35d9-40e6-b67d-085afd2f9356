# -*-coding:utf-8-*-
"""
量化感知训练（QAT):
    Use OpenVINO™ NNCF 对YOLOv8n-pose模型进行量化
0. 安装库 命令：
pip install ultralytics "openvino-dev[pytorch]" nncf torch torchvision --extra-index-url https://download.pytorch.org/whl/cpu

1. 加载训练后的pt模型和训练集
2. 使用nncf.quantize API 量化模型
3. 微调 (Fine-tuning) 训练
4. 导出包含量化信息的onnx
"""
import torch

import nncf             # 导入NNCF库
from openvino.tools import mo
from openvino.runtime import serialize
from nncf.torch.initialization import register_default_init_args
from nncf.torch import create_compressed_model, register_default_init_args



from ultralytics import YOLO
from ultralytics.cfg import get_cfg

# 1. 加载你的预训练FP32 YOLOv8n-pose模型
# 注意：我们这里加载的是PyTorch原生的nn.Module，而不是Ultralytics的YOLO对象
# 这对于NNCF的集成更友好
model_path = "yolov8n-pose.pt"
model = YOLO(model_path).model.cpu()
model.eval()

print("FP32模型加载成功！")


# 2. 准备数据集
# 使用Ultralytics的内部函数来帮助创建数据加载器
data_yaml_path = "my_pose_dataset.yaml"
args = get_cfg(cfg='ultralytics/cfg/default.yaml')
args.data = data_yaml_path
args.batch_size = 16            # 根据你的硬件调整

# 获取训练数据加载器
# 这是QAT微调所需的数据源
train_loader = YOLO(model_path).get_trainer(args=args).get_dataloader(data_yaml_path, batch_size=args.batch_size, mode="train")

# NNCF需要一个小的校准数据集来初始化量化参数
# 通常用一小部分训练集即可
calibration_loader = YOLO(model_path).get_trainer(args=args).get_dataloader(data_yaml_path, batch_size=args.batch_size, mode="train")


# 为了让NNCF知道如何从复杂的数据批次中提取图像，我们需要一个转换函数
def transform_fn(data_batch):
    # YOLO的数据加载器返回一个字典，图像在'img'键下
    return data_batch["img"].float() / 255.0


# 将数据加载器和转换函数包装成NNCF需要的数据集对象
calibration_dataset = nncf.Dataset(calibration_loader, transform_fn)

print("数据加载器准备完成！")



# ------------------------------------------------- 3. 应用NNCF进行量化感知训练的准备 ----------------------------------------------
# subset_size决定了用多少数据来初始化（校准）量化参数
# Preset.PERFORMANCE 是一种性能优先的量化配置（权重和激活都对称量化）
# Preset.MIXED 可以获得更好的精度，但速度稍慢
qat_model = nncf.quantize(
    model=model,
    calibration_dataset=calibration_dataset,
    preset=nncf.Preset.PERFORMANCE,
    subset_size=100  # 使用100个批次的数据进行校准
)

print("NNCF量化节点已插入模型！")



# ------------------------------------------------- 4. 微调QAT模型 --------------------------------------------------------------
# NNCF在quantize函数后会返回一个Compression-aware的模型控制器和模型本身
compression_ctrl, qat_model = create_compressed_model(model, config=register_default_init_args(qat_model.nncf_config,
                                                                                               calibration_dataset))

# 定义优化器和损失函数
# 注意：损失函数应该使用YOLOv8自带的，这里为了演示，我们用一个占位符
# 在实际应用中，你需要从YOLO的Trainer中获取损失函数
optimizer = torch.optim.Adam(qat_model.parameters(), lr=1e-4)
loss_fn = YOLO(model_path).get_trainer(args=args).criterion  # 获取YOLOv8的损失函数

# 微调几个周期
num_epochs = 5  # QAT微调通常不需要很多epoch
print("开始进行QAT微调...")
for epoch in range(num_epochs):
    qat_model.train()
    for batch in train_loader:
        # 将数据移动到设备
        images = batch["img"].float().to("cuda" if torch.cuda.is_available() else "cpu") / 255.0

        # 前向传播
        optimizer.zero_grad()
        outputs = qat_model(images)

        # 计算损失 (这部分需要与YOLOv8的损失函数对齐)
        # 此处为一个概念性演示
        loss, _ = loss_fn(outputs, batch)

        # 反向传播和优化
        compression_ctrl.scheduler.pre_backward_step()  # NNCF要求在backward前调用
        loss.backward()
        compression_ctrl.scheduler.pre_optimizer_step()  # NNCF要求在optimizer.step前调用
        optimizer.step()

    print(f"Epoch {epoch + 1}/{num_epochs}, Loss: {loss.item()}")

print("QAT微调完成！")



# ------------------------------------------------ 5. 导出为ONNX，然后转换为OpenVINO IR -------------------------------------------
dummy_input = torch.randn(1, 3, 640, 640).to("cuda" if torch.cuda.is_available() else "cpu")
onnx_path = "yolov8n-pose_qat.onnx"
output_dir = "openvino_ir_qat"

# 禁用NNCF的压缩，以导出干净的、可部署的模型
with nncf.torch.disable_tracing():
    torch.onnx.export(qat_model, dummy_input, onnx_path, opset_version=11)

print(f"QAT模型已导出到 {onnx_path}")

# 使用OpenVINO Model Optimizer (MO) 转换为IR格式 (FP16或INT8)
# MO会自动检测ONNX中的伪量化节点并生成INT8模型
ov_model = mo.convert_model(onnx_path, compress_to_fp16=False)              # compress_to_fp16=False 才会生成INT8

# 序列化为.xml和.bin文件

serialize(ov_model, xml_path=f"{output_dir}/yolov8n-pose_qat.xml")

print(f"OpenVINO INT8 IR模型已生成在 {output_dir} 目录中！")


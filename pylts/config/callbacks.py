# -*-coding:utf-8-*-
"""
created by @Moss 20250624
Hook 钩子允许你在不修改核心训练/推理代码的情况下，在流程的特定“事件点”插入自定义的逻辑。
    创建一个Callbacks类，将训练、验证和测试过程中所有常用的 Hook 操作封装起来，使其成为一个可插拔、可重用的“生命周期监视器”。
优点：解耦与模块化, 可扩展性, 可重用性, 代码可读性

框架导入: class Callback, class CallbackHooks(Callback)
TODO 训练核心： class Trainer.__init__(callbacks), Trainer.Call_HOOK(event_name)

TODO 新增钩子：
1.【框架层】在 Trainer 中找到合适的位置，调用新钩子。
self.Call_HOOK('on_validation_end')         # [STEP 1] 在验证流程结束后，立即调用新钩子
2.【文档层】在 Callback 基类中声明新钩子。
def on_validation_end(self): pass           # [STEP 2] 在基类中声明新钩子，作为“官方文档”
3.【应用层】在你的具体回调类 (CallbackHooks) 中实现新钩子的逻辑：
def on_validation_end(self):        # [STEP 3] 在回调类中具体实现新钩子的逻辑

调用: hooks = CallbackHooks(save_dir=temp_dir), Trainer(callbacks=[hooks])
"""
import os
import torch
import torch.nn as nn
from torch.utils.data import TensorDataset, DataLoader
from tqdm import tqdm
import tempfile
import shutil



class Callback:
    """回调的通用基类。"""

    def set_trainer(self, trainer):
        self.trainer = trainer

    def on_train_begin(self): pass

    def on_train_end(self): pass

    def on_epoch_begin(self): pass

    def on_epoch_end(self): pass

    def on_batch_begin(self): pass

    def on_batch_end(self): pass

    # [STEP 2] 在基类中声明新钩子，作为“官方文档”
    def on_validation_end(self): pass


class CallbackHooks(Callback):
    """一个适配自定义Trainer的回调类，负责日志记录和 模型保存。"""

    def __init__(self, save_dir="."):
        self.save_dir = save_dir
        self.best_val_accuracy = 0.0
        os.makedirs(self.save_dir, exist_ok=True)

    def on_train_begin(self):
        print("\n" + "=" * 50)
        print("🚀 自定义训练循环开始！")
        print(f"模型将保存在: {self.save_dir}")
        self.best_val_accuracy = 0.0
        print("=" * 50)

    def on_epoch_begin(self):
        print(f"\n--- Epoch {self.trainer.epoch + 1}/{self.trainer.max_epochs} 开始 ---")

    def on_epoch_end(self):
        # 这个钩子在验证之后被调用，非常适合用来判断是否保存模型
        val_acc = self.trainer.metrics.get('val_accuracy', 0.0)
        if val_acc > self.best_val_accuracy:
            self.best_val_accuracy = val_acc
            print(f"🎉 新的最佳模型！准确率达到: {self.best_val_accuracy:.2f}%")
            best_model_path = os.path.join(self.save_dir, 'best_model.pth')
            torch.save(self.trainer.model.state_dict(), best_model_path)
            print(f"最佳模型已保存至: {best_model_path}")

    def on_train_end(self):
        print("\n" + "=" * 50)
        print("✅ 训练全部完成！")
        print(f"整个训练过程中最佳的验证准确率是: {self.best_val_accuracy:.2f}%")
        print("=" * 50)

    # [STEP 3] 在回调类中具体实现新钩子的逻辑
    def on_validation_end(self):
        """在验证结束后，打印详细的验证结果。"""
        val_acc = self.trainer.metrics.get('val_accuracy', 0.0)
        val_loss = self.trainer.metrics.get('val_loss', 0.0)
        print(f"--- Epoch {self.trainer.epoch + 1} 验证结束 ---")
        print(f"    验证损失: {val_loss:.4f}, 验证准确率: {val_acc:.2f}%")


# 示例 训练框架
class Trainer:
    """参考的基础训练框架：
    需要定义 self.callbacks, Call_HOOK()
    """


    def __init__(self, model, train_loader, val_loader, criterion, optimizer, callbacks=None):
        self.model = model
        self.train_loader = train_loader
        self.val_loader = val_loader
        self.criterion = criterion
        self.optimizer = optimizer

        self.callbacks = callbacks if callbacks else []
        for cb in self.callbacks:
            cb.set_trainer(self)

        self.epoch = 0
        self.max_epochs = 0
        self.metrics = {}

    def Call_HOOK(self, event_name):
        for cb in self.callbacks:
            if hasattr(cb, event_name):
                getattr(cb, event_name)()

    def _run_validation(self):
        self.model.eval()
        val_loss, correct, total = 0, 0, 0
        with torch.no_grad():
            for data, targets in self.val_loader:
                outputs = self.model(data)
                val_loss += self.criterion(outputs, targets).item()
                _, predicted = torch.max(outputs.data, 1)
                total += targets.size(0)
                correct += (predicted == targets).sum().item()

        self.metrics['val_loss'] = val_loss / len(self.val_loader)
        self.metrics['val_accuracy'] = 100 * correct / total

        # [STEP 1] 在验证流程结束后，立即调用新钩子
        self.Call_HOOK('on_validation_end')

        self.model.train()

    def train(self, epochs):
        self.max_epochs = epochs
        self.model.train()
        self.Call_HOOK('on_train_begin')
        for epoch in range(epochs):
            self.epoch = epoch
            self.Call_HOOK('on_epoch_begin')
            pbar = tqdm(self.train_loader, desc=f"Epoch {epoch + 1}/{epochs}")
            for _, (data, targets) in enumerate(pbar):
                self.Call_HOOK('on_batch_begin')
                self.optimizer.zero_grad()
                outputs = self.model(data)
                loss = self.criterion(outputs, targets)
                loss.backward()
                self.optimizer.step()
                pbar.set_postfix(loss=loss.item())
                self.Call_HOOK('on_batch_end')

            self._run_validation()
            self.Call_HOOK('on_epoch_end')
        self.Call_HOOK('on_train_end')




if __name__ == '__main__':
    # 1. 定义一个简单的自定义模型
    class SimpleClassifier(nn.Module):
        def __init__(self):
            super().__init__()
            self.layer1 = nn.Linear(20, 128)
            self.relu = nn.ReLU()
            self.layer2 = nn.Linear(128, 10)

        def forward(self, x):
            return self.layer2(self.relu(self.layer1(x)))


    # 2. 创建虚拟数据和加载器
    X_train = torch.randn(500, 20)
    y_train = torch.randint(0, 10, (500,))
    X_val = torch.randn(100, 20)
    y_val = torch.randint(0, 10, (100,))
    train_loader = DataLoader(TensorDataset(X_train, y_train), batch_size=32, shuffle=True)
    val_loader = DataLoader(TensorDataset(X_val, y_val), batch_size=32)

    # 3. 实例化所有组件
    model = SimpleClassifier()
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    criterion = nn.CrossEntropyLoss()

    # 4. 实例化回调工具
    temp_dir = tempfile.mkdtemp()
    hooks = CallbackHooks(save_dir=temp_dir)

    # 5. 实例化 Trainer，并传入所有组件和回调
    trainer = Trainer(
        model=model,
        train_loader=train_loader,
        val_loader=val_loader,
        criterion=criterion,
        optimizer=optimizer,
        callbacks=[hooks]
    )
    # 6. 启动训练！
    trainer.train(epochs=3)

    # --- 清理 ---
    print(f"\n检查保存的权重文件: {os.listdir(temp_dir)}")
    shutil.rmtree(temp_dir)
    print("临时目录已清理。")
# -*-coding:utf-8-*-
"""
| 特性         | DP（DataParallel）   | DDP（Distributed DataParallel）    |
| ----------   | ------------------  | -------------------------------- |
| **通信方式**     | 通过 CPU 进行梯度聚合和参数广播  | 使用分布式通信后端（如 NCCL）在 GPU 之间直接通信    |
| **适用场景**     | 单机多卡训练                   | 单机多卡或多机多卡训练                              |
| **CPU- 开销**    | 高，因为需要 CPU 参与通信       | 低，通信直接在 GPU 之间进行                      |
| **扩展性**       | 仅限单机多卡，无法扩展到多机     | 支持多机扩展，适合大规模分布式训练                |
| **实现复杂度**    | 简单，易于使用                 | 相对复杂，需要多进程管理                         |
| **梯度通信效率**  | 较低                          | 较高                                        |
| **模型参数存储**  | 模型参数存储在 CPU 上           | 模型参数存储在 GPU 上                         |
| **代码修改需求**  | 代码修改较少                    | 需要使用 `torch.distributed` 模块进行初始化    |

分布式数据并行 DDP(Distribute DataParallel)： 可用于单机多卡 和 多机多卡 训练，适用于单GPU能够加载一个完整模型
    (apex混合精度训练)
    1.初始化进程组
    2. 模型转ddp模型 DDP(model, device_ids=[gpu])
        TODO SyncBN： BN需要对所有数据处理，若每个进程batchsize较小，则使用BN层作用很小
        dist.barrier() ：等待, 同步所有进程. 用于等待所有进程完成模型参数初始化；或 等待所有进程计算验证集本地指标
    3. 数据分布式并行（内部会根据rank采样）, 分布式采样器DistributedSampler
    4. 训练过程中的同步参数，梯度累计【可选】
    5.  销毁进程组上下文数据
    TODO ckpt的保存，需要制定单个进程完成； rank对进程编号

配置说明：
    node：物理节点(多节点表示多个机器/容器)            # nnodes 节点数量 [例如2台机器, nnodes=2]
    local_rank: 在一个容器(node)上进程的相对序号；    # rank为全局(整个分布式任务中)的进程序号
    world_size: 全局中，rank的数量                  # 例如2机器,每台机器8卡，共16张卡，world_size=16
    nproc_per_node： 每个node(物理节点)上进程的数量
    master_addr 和 master_port ：多机多卡需配置每个机器的ip和端口
    num_workers 并行加载数据的进程数量。 num_workers = GPU数量 * 每个GPU的进程数, 可以充分利用多核CPU的并行能力，加快数据的读取和预处理速度

启动方式：
    import torch.multiprocessing as mp
    world_size = 2
    # 必须放在if __name__ == '__main__':下
    mp.spawn(train, args=(world_size,), nprocs=world_size, join=True)

    单机多卡            # torchrun可替换为 python -m torch.distributed.launch
    CUDA_VISIBLE_DEVICES="0,1,2,3" torchrun --nproc_per_node=$GPUS --nnodes=1 train.py --dist-url 'env://'          # dist-url 也可设置为tcp://localhost:23456
    多(2)机多(8)卡      # node_rank是唯一标识符， 例如：
    torchrun  --nnodes=2  --nproc_per_node=8  --node_rank=0  --master_addr=************* --master_port=23456  train.py  # 机器1运行脚本(主节点rank 0)
    torchrun  --nnodes=2  --nproc_per_node=8  --node_rank=1  --master_addr=************* --master_port=23456  train.py  # 机器2运行脚本

"""
import os
import random

import torch
import torch.multiprocessing as mp
from torch.nn.parallel import DistributedDataParallel as DDP
import torch.distributed as dist
from torch.utils.data import DataLoader, DistributedSampler



__all__ = ['init_dist', 'get_dist_info']      # 限制使用 from xx import * 仅能导入__all__中指定的对象

# 1. 添加超时设置防止无限等待
import signal


def timeout_handler(signum, frame):
    print("DDP初始化超时!")
    raise TimeoutError("DDP initialization timeout")


def setup_distributed_debug():
    """正确初始化分布式环境"""

    # 1. 检查环境变量
    print("=== 分布式环境检查 ===")
    print(f"RANK: {os.environ.get('RANK', 'Not set')}")
    print(f"LOCAL_RANK: {os.environ.get('LOCAL_RANK', 'Not set')}")
    print(f"WORLD_SIZE: {os.environ.get('WORLD_SIZE', 'Not set')}")
    print(f"MASTER_ADDR: {os.environ.get('MASTER_ADDR', 'Not set')}")
    print(f"MASTER_PORT: {os.environ.get('MASTER_PORT', 'Not set')}")
    print(f"CUDA_VISIBLE_DEVICES: {os.environ.get('CUDA_VISIBLE_DEVICES', 'Not set')}")

    # 2. 获取正确的rank和local_rank
    if 'RANK' in os.environ and 'LOCAL_RANK' in os.environ:
        rank = int(os.environ['RANK'])
        local_rank = int(os.environ['LOCAL_RANK'])
        world_size = int(os.environ['WORLD_SIZE'])
    else:
        raise ValueError("环境变量RANK, LOCAL_RANK, WORLD_SIZE未正确设置")

    print(f"Process rank: {rank}, local_rank: {local_rank}, world_size: {world_size}")

    # 3. 设置设备
    torch.cuda.set_device(local_rank)
    device = torch.device(f'cuda:{local_rank}')
    print(f"设置设备为: {device}")

    # 4. 初始化进程组（如果还没初始化）
    if not dist.is_initialized():
        print("初始化分布式进程组...")
        dist.init_process_group(
            backend='nccl',
            init_method='env://',
            world_size=world_size,
            rank=rank
        )
        print("分布式进程组初始化完成")

    return rank, local_rank, world_size, device


def create_ddp_model_debug(model, local_rank):
        """正确创建DDP模型"""

        # 1. 确保模型在正确的设备上
        device = torch.device(f'cuda:{local_rank}')
        model = model.to(device)
        print(f"模型已移动到设备: {device}")

        # 2. 添加同步点确保所有进程准备就绪
        if dist.is_initialized():
            print("等待所有进程同步...")
            dist.barrier()
            print("所有进程同步完成")

        # 3. 创建DDP模型 - 使用local_rank而不是args.gpu
        print(f"创建DDP模型，device_ids=[{local_rank}]")
        try:
            ddp_model = torch.nn.parallel.DistributedDataParallel(
                model,
                device_ids=[local_rank],
                output_device=local_rank,
                find_unused_parameters=False  # 如果有未使用的参数，设为True
            )
            print("DDP模型创建成功")
            return ddp_model
        except Exception as e:
            print(f"DDP模型创建失败: {e}")
            raise


def init_dist(backend='nccl', init_method="env://", **kwargs):

    if init_method == "env://":
        if 'MASTER_ADDR' not in os.environ:
            os.environ['MASTER_ADDR'] = '127.0.0.1'     # 默认值 仅能用于单机
        if 'MASTER_PORT' not in os.environ:
            os.environ['MASTER_PORT'] = str(random.randint(1024, 65535))     # 1024~65535  netstat查看已被占用的端口
        if 'RANK' not in os.environ:
            os.environ['RANK'] = '0'
        if 'WORLD_SIZE' not in os.environ:
            os.environ['WORLD_SIZE'] = '1'          # len of rank

    rank = int(os.environ['RANK'])          # 多卡训练时每个进程上值不同
    torch.cuda.set_device(rank % torch.cuda.device_count())       # 初始化时，每个进程对应一个RANK
    dist.init_process_group(backend=backend, **kwargs)


def get_dist_info() ->(int, int):
    """
    from torch.distributed get rank and world_size
    :return: rank, world_size
    """
    if dist.is_available() and dist.is_initialized():
        rank = dist.get_rank()
        world_size = dist.get_world_size()
    else:
        rank = 0
        world_size = 1
    return rank, world_size



class DistributedTrainer:
    def __init__(self, model, train_dataset, val_dataset=None, batch_size=64, backend='nccl', init_method="env://", **kwargs):
        """
        初始化分布式训练器
        :param model: 要训练的模型
        :param train_dataset: 训练数据集
        :param val_dataset: 验证数据集（可选）
        :param batch_size: 每个GPU的batch size
        :param epochs: 训练轮数
        :param backend: 分布式通信后端（默认为 'nccl', 专为GPU训练优化）
        :param init_method: 初始化方法（默认为 'env://'）
        :param kwargs: 其他传递给 `init_process_group` 的参数
        """
        self.model = model

        self.train_dataset = train_dataset
        self.val_dataset = val_dataset

        self.batch_size = batch_size
        self.backend = backend
        self.init_method = init_method
        self.kwargs = kwargs

        # 初始化分布式环境
        self._init_distributed()
        # 获取分布式信息
        self.rank, self.world_size = self._get_dist_info()

        # 1. 包装模型为分布式模型
        self.model = self._wrap_model()

        # 2. 准备数据加载器
        self.num_workers = kwargs.get("num_workers", torch.cuda.device_count())     # 并行加载数据的进程数量
        self.train_loader = self._prepare_data_loader(train_dataset, shuffle=True)
        self.val_loader = self._prepare_data_loader(val_dataset, shuffle=False) if val_dataset else None

    def _init_distributed(self):
        """
        初始化分布式环境
        """
        # 初始化 local_rank
        self.local_rank = int(os.environ.get("LOCAL_RANK", 0))

        if self.init_method == "env://":
            if 'MASTER_ADDR' not in os.environ:
                os.environ['MASTER_ADDR'] = '127.0.0.1'  # 默认值，仅适用于单机
            if 'MASTER_PORT' not in os.environ:
                os.environ['MASTER_PORT'] = str(random.randint(1024, 65535))  # 随机分配端口
            if 'RANK' not in os.environ:
                os.environ['RANK'] = '0'
            if 'WORLD_SIZE' not in os.environ:
                os.environ['WORLD_SIZE'] = '1'

        # 设置当前进程的设备
        torch.cuda.set_device(self.local_rank)


        # 初始化分布式进程组
        dist.init_process_group(backend=self.backend, init_method=self.init_method, **self.kwargs)

    def _get_dist_info(self):
        """
        获取分布式环境中的 rank 和 world_size
        :return: rank, world_size
        """
        if dist.is_available() and dist.is_initialized():
            rank = dist.get_rank()
            world_size = dist.get_world_size()
        else:
            rank = 0
            world_size = 1
        return rank, world_size

    def _prepare_data_loader(self, dataset, shuffle=False):
        """
        准备分布式数据加载器
        """
        if dataset is None:
            return None
        sampler = DistributedSampler(dataset, shuffle=shuffle)
        return DataLoader(
            dataset,
            batch_size=self.batch_size,
            shuffle=False,
            sampler=sampler,
            pin_memory=True,
            num_workers=self.num_workers,  # 默认使用4个工作线程
        )

    def _wrap_model(self):
        """
        包装模型为分布式模型
        """
        model = self.model.to(torch.cuda.current_device())
        if dist.is_available() and dist.is_initialized():
            model = DDP(model, device_ids=[torch.cuda.current_device()])
        return model

    def train(self):
        """
        空的训练方法，由用户在主函数中实现具体逻辑
        """
        raise NotImplementedError("The `train` method should be implemented in the main script.")

    def __del__(self):
        """
        清理分布式环境
        """
        if dist.is_available() and dist.is_initialized():
            dist.destroy_process_group()



class DDP_Trainer(DistributedTrainer):
    def train(self):        # TODO 重写 自定义训练方式
        self.epochs = 100
        for epoch in range(self.epochs):
            self.model.train()
            total_loss = 0.0
            for inputs, labels in self.train_loader:
                inputs, labels = inputs.to(self.local_rank), labels.to(self.local_rank)
                self.optimizer.zero_grad()
                outputs = self.model(inputs)
                loss = self.loss_fn(outputs, labels)
                loss.backward()
                self.optimizer.step()
                total_loss += loss.item()
            avg_loss = total_loss / len(self.train_loader)
            if self.local_rank == 0:
                print(f"Epoch [{epoch+1}/{self.epochs}], Train Loss: {avg_loss:.4f}")



def Get_your_Module():
    from torchvision.transforms import transforms
    # 定义模型
    model = torch.nn.Linear(in_features=10, out_features=4)
    # 数据预处理
    transform = transforms.Compose([transforms.ToTensor(), transforms.Normalize((0.5,), (0.5,))])
    train_dataset = datasets.MNIST(root='./data', train=True, download=True, transform=transform)
    val_dataset = datasets.MNIST(root='./data', train=False, download=True, transform=transform)
    return model, train_dataset, val_dataset


# example2: _train()
def _train_2(gpu):  # 示例主函数
    print(f"GPU: {gpu}")
    init_dist(local_rank=0, backend='nccl')  # 1.
    rank, world_size = get_dist_info()
    print(f"dist.is_initialized, rank:{rank}, world_size:{world_size}")

    print(f"train_sampler=torch.utils.data.distributed.DistributedSampler(dataset)")  # 2. TODO 示例
    print(f"dataloader=torch.utils.data.DataLoader(dataset, batchsize, sample=train_sampler)")
    model = torch.nn.Linear(in_features=10, out_features=4)

    model = DDP(model.cuda(), device_ids=[rank])  # 3.

    for epoch in range(3):
        print(f"epoch-{epoch} dataloader loss ...{model}")  # 4

    if rank == 0:
        print(f"use torch.save(model, 'My_model.pth')")  # 5

    return


# example1:
def _train_1():
    # 主函数示例
    model, train_dataset, val_dataset = Get_your_Module()

    # 创建分布式训练器
    trainer = DDP_Trainer(
        model=model,
        train_dataset=train_dataset,
        val_dataset=val_dataset,
        batch_size=64,
        backend='nccl',
        init_method="env://"
    )

    # 开始训练
    trainer.train()




if __name__ == '__main__':
    # TODO 代码使用方法 参考 _train_1() 或 _train_2()
    print("Ref:DistributedTrainer and DDP_Trainer")
    # TODO 启动方法
    # 1. 该启动方式 必须放在if __name__ == '__main__':下 * train_1 是py文件,里面包含 _train_2函数 中的内容
    print("mp.spawn(train_2, nprocs=torch.cuda.device_count(), args=())")
    # 2. 推荐启动方式(单机多卡)
    print('CUDA_VISIBLE_DEVICES="0,1,2,3" torchrun --nproc_per_node=$GPUS --nnodes=1 train_2.py')


    # ===== 额外的调试技巧 =====
    # 在创建DDP之前添加：
    signal.signal(signal.SIGALRM, timeout_handler)
    signal.alarm(60)  # 60秒超时

    # DDP创建代码...

    signal.alarm(0)  # 取消超时

    # 2. 检查NCCL配置
    os.environ['NCCL_DEBUG'] = 'INFO'  # 开启NCCL调试信息
    os.environ['NCCL_TIMEOUT'] = '1800'  # 设置NCCL超时时间

    # 3. 如果仍然卡住，尝试使用gloo后端进行调试
    # dist.init_process_group(backend='gloo', ...)  # 用于调试







# -*-coding:utf-8-*-
"""
1. 多卡训练时，自动评估显存占用最少的卡排序，根据输入需要的卡的数量(int:num_gpus),
输出适合的卡id； 例如输入：3， 输出'1,2,4' 表示1,2,4号卡显存占用最小
然后将输出结果传递给select_device()调用对应的卡

也可以直接输入需要的卡id，例如get_free_gpu_ids(num_gpus='2,3'),则直接返回该字符串'2,3',
然后再在select_device()调用对应的卡

2. 单卡或多卡机器的单GPU训练时，输入方式同理

Usage:
    device = 1          # 1, 用1张空闲的卡； '1', 用1号卡, '0,3'指定用0号卡和3号卡
    device = select_device(get_free_gpu_ids(num_gpus=device), batch_size=128)
    print(device, device.__class__)  # 打印出利用率最低的xx张卡的id

"""
import subprocess
import re, os
import torch


def get_free_gpu_ids(num_gpus:any) ->str:
    """
    # 默认至少1张GPU卡
    :param num_gpus: 要用几张卡，整数       # 建议 1,2,3...8
    :return: '0' or such as devices '1,2'
    """
    if isinstance(num_gpus, str):
        return num_gpus         # 传入字符串 不做任何处理

    sp = subprocess.Popen(['nvidia-smi', '-q'], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    out_list = str(sp.communicate()[0]).split('\\n')
    gpu_num = len([item for item in out_list if item.startswith('GPU')])  # GPU数量(use nvidia-smi -L)
    assert num_gpus <= gpu_num, f"@Moss: only have gpu num {gpu_num}, get use {num_gpus}"
    if gpu_num == 1:
        return '0'          # 单卡模式

    gpu_info = []           # 多卡寻优
    for item in out_list:
        if 'Minor Number' in item:
            gpu_info.append({'id': int(re.findall(r': (.*)', item)[0])})
        if 'FB Memory Usage' in item:
            next_is_gpu_util = True
        if 'Total' in item and next_is_gpu_util:
            gpu_info[-1]['total_mem'] = float(re.findall(r': (.*) MiB', item)[0])
            next_is_gpu_util = False
        if 'Used' in item:
            gpu_info[-1]['used_mem'] = float(re.findall(r': (.*) MiB', item)[0])

    for gpu in gpu_info:
        gpu['mem_util'] = gpu['used_mem'] / gpu['total_mem'] *100
        # print(gpu['mem_util'])
    gpu_info = sorted(gpu_info, key=lambda x: x['mem_util'])
    least_used_gpus = [gpu['id'] for gpu in gpu_info[:num_gpus]]        # 显存利用率由低到高: []
    devices = ','.join(str(idx) for idx in least_used_gpus)

    return devices


def select_device(device:str, batch_size:any):
    # device = 'cpu' or '0' or '0,1,2,3'
    s = f'Using torch {torch.__version__} '  # string
    cpu = device.lower() == 'cpu'
    if cpu:
        os.environ['CUDA_VISIBLE_DEVICES'] = '-1'  # force torch.cuda.is_available() = False
    elif device:  # non-cpu device requested
        os.environ['CUDA_VISIBLE_DEVICES'] = device  # set environment variable
        assert torch.cuda.is_available(), f'CUDA unavailable, invalid device {device} requested'  # check availability

    cuda = torch.cuda.is_available() and not cpu
    if cuda:
        n = torch.cuda.device_count()
        if n > 1 and batch_size:  # check that batch_size is compatible with device_count
            assert batch_size % n == 0, f'batch-size {batch_size} not multiple of GPU count {n}'
        space = ' ' * len(s)
        for i, d in enumerate(device.split(',') if device else range(n)):
            p = torch.cuda.get_device_properties(i)
            s += f"{'' if i == 0 else space}CUDA:{d} ({p.name}, {p.total_memory / 1024 ** 2}MB)\n"  # bytes to MB
    else:
        s += 'CPU'
    print(os.environ['CUDA_VISIBLE_DEVICES'])
    # logger.info(f'{s}\n')  # skip a line
    return torch.device('cuda:0' if cuda else 'cpu')


if __name__ == '__main__':
    device = 1          # 1, 用1张空闲的卡； '1', 用1号卡
    device = select_device(get_free_gpu_ids(num_gpus=device), batch_size=128)
    print(device, device.__class__)  # 打印出利用率最低的xx张卡的id
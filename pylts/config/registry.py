"""
created by @Moss 20250311 21:41
# Copyright (c) OpenMMLab. Registry是一种用于管理模块（如类或函数）的机制，通过字符串来映射 和 实例化模块。
常用于需要 动态配置 和 切换模块的场景。

* 流程说明:
1. 创建层级注册器:  MODELS = Registry('models', scope='pylts')
2. 注册模块:       @MODELS.register_module()

3. 使用根注册器(MODELS)构建:    detector_instance = MODELS.build(detector_cfg)
                                                    # detector_cfg 为配置字典：detector_cfg_scoped = dict(type='pyldet.FasterRCNN')

模块注册
注册器可以将特定的类或函数与一个字符串标识符关联起来。例如，将一个自定义的神经网络层与字符串 "CustomLayer" 关联，在配置文件中可以通过字符串("CustomLayer")引用该层。

模块实例化
通过注册器，可以根据配置字典（通常包含 type 键，其值为注册的字符串标识符）来实例化相应的模块。在不修改代码的情况下，仅通过修改配置就能切换不同的模块实现。

层级注册器支持
注册器支持层级结构，子注册器可以继承父注册器中的模块。这意味着在一个大型项目中，不同子项目的模块可以组织在不同的注册器下，同时还能共享父注册器中的通用模块。

自动导入模块
注册器可以指定模块所在的文件路径（locations），在实例化模块时会自动导入这些路径中的模块，从而无需手动导入。
"""
import logging
import importlib
import inspect
from collections import abc
from collections.abc import Callable
from contextlib import contextmanager
from importlib import import_module
from typing import Any, Dict, Generator, List, Optional, Tuple, Type, Union

from rich.console import Console
from rich.table import Table


from ConfigDict import ConfigDict



def print_log(msg, logger=None, level=logging.INFO):
    """
    TODO: the follows from mmcv.utils.logging.py
    Print a log message.

    Args:
        msg (str): The message to be logged.
        logger (logging.Logger | str | None): The logger to be used.
            Some special loggers are:
            - "silent": no message will be printed.
            - other str: the logger obtained with `get_root_logger(logger)`.
            - None: The `print()` method will be used to print log messages.
        level (int): Logging level. Only available when `logger` is a Logger
            object or "root".
    """
    from pylts.utils.statistics.Mod_logger import init_logger

    if logger is None:
        print(msg)
    elif isinstance(logger, logging.Logger):
        logger.log(level, msg)
    elif logger == 'silent':
        pass
    elif isinstance(logger, str):
        _logger = init_logger(logger)
        _logger.log(level, msg)
    else:
        raise TypeError(
            'logger should be either a logging.Logger object, str, '
            f'"silent" or None, but got {type(logger)}')



def is_seq_of(seq: Any,
              expected_type: Union[Type, tuple],
              seq_type: Optional[Type] = None) -> bool:
    """检查一个变量是否是特定类型的序列。

    Args:
        seq (Any): 要检查的序列。
        expected_type (type or tuple): 序列中元素的期望类型。
        seq_type (type, optional): 序列本身的期望类型, 默认为 abc.Sequence。

    Returns:
        bool: 如果 `seq` 是有效序列则返回 True，否则 False。

    Examples:
        >>> is_seq_of(['a', 'b'], str)
        True
        >>> is_seq_of([1, 2, 'a'], int)
        False
        >>> is_seq_of(('a', 'b'), str, seq_type=tuple)
        True
    """
    if seq_type is None:
        exp_seq_type = abc.Sequence
    else:
        assert isinstance(seq_type, type)
        exp_seq_type = seq_type
    if not isinstance(seq, exp_seq_type):
        return False
    for item in seq:
        if not isinstance(item, expected_type):
            return False
    return True


def build_from_cfg(
        cfg: Union[dict, ConfigDict],
        registry,  # type: Registry
        default_args: Optional[Union[dict, ConfigDict]] = None) -> Any:
    """根据配置字典构建一个模块。

    如果配置的是一个类，则实例化该类；如果是一个函数，则调用该函数。
    `cfg` 和 `default_args` 中至少有一个必须包含 'type' 键。
    'type' 的值是注册在 `registry` 中的字符串。

    Args:
        cfg (dict or ConfigDict): 配置字典，至少包含 "type" 键。
        registry (:obj:`Registry`): 用于查找 type 的注册器。
        default_args (dict or ConfigDict, optional): 默认的初始化参数。

    Returns:
        object: 构建好的对象。

    Examples:
        >>> MODELS = Registry('models')
        >>> @MODELS.register_module()
        >>> class ResNet:
        ...     def __init__(self, depth, stages=4):
        ...         self.depth = depth
        ...         self.stages = stages
        >>> cfg = dict(type='ResNet', depth=50)
        >>> model = build_from_cfg(cfg, MODELS)
        >>> print(model.depth)
        50
    """
    if not isinstance(cfg, (dict, ConfigDict)):
        raise TypeError(f'cfg 应为 dict 或 ConfigDict，但得到 {type(cfg)}')

    if 'type' not in cfg:
        if default_args is None or 'type' not in default_args:
            raise KeyError('`cfg` 或 `default_args` 必须包含 "type" 键')

    if not isinstance(registry, Registry):
        raise TypeError(f'registry 必须是 Registry 对象，但得到 {type(registry)}')

    if not (isinstance(default_args, (dict, ConfigDict)) or default_args is None):
        raise TypeError(f'default_args 应为 dict, ConfigDict 或 None，但得到 {type(default_args)}')

    args = cfg.copy()
    if default_args is not None:
        for name, value in default_args.items():
            args.setdefault(name, value)  # 用默认参数填充缺失的键

    # 如果配置中指定了 `_scope_`，则临时切换到该作用域进行构建
    scope = args.pop('_scope_', None)
    with registry.switch_scope_and_registry(scope) as registry:
        obj_type = args.pop('type')
        if isinstance(obj_type, str):
            # 从注册器中获取类或函数
            obj_cls = registry.get(obj_type)
            if obj_cls is None:
                raise KeyError(
                    f'{obj_type} 不在 {registry.scope}::{registry.name} 注册器中。'
                    '请检查值是否正确或模块是否已注册。')
        elif callable(obj_type):
            # 'type' 也可以直接是一个类或函数，而不是字符串
            obj_cls = obj_type
        else:
            raise TypeError(f'type 必须是 str 或可调用对象，但得到 {type(obj_type)}')

        # 如果 obj_cls 继承了 ManagerMixin，则使用 get_instance 以获取单例
        if inspect.isclass(obj_cls) and issubclass(obj_cls, ManagerMixin):
            obj = obj_cls.get_instance(**args)
        else:
            # 否则，直接实例化或调用
            obj = obj_cls(**args)

        if inspect.isclass(obj_cls) or inspect.isfunction(obj_cls) or inspect.ismethod(obj_cls):
            print_log(
                f'已从注册器构建一个 `{obj_cls.__name__}` 实例，'
                f'其实现位于 {obj_cls.__module__}',
                level=logging.DEBUG)
        else:
            print_log(f'已从注册器构建一个实例，其构造器为 {obj_cls}', level=logging.DEBUG)
        return obj


# 在build_from_cfg中，如果一个类继承了 ManagerMixin， 它会通过 get_instance() 而不是常规的 __init__() 来获取实例，以确保全局唯一。
class ManagerMixin:
    """用于创建可全局访问的单例实例的混入类。"""
    _instances = {}

    @classmethod
    def get_instance(cls, instance_name: Optional[str] = None, **kwargs):
        """获取或创建类的单例实例。"""
        if instance_name is None:
            instance_name = cls.__name__
        if instance_name not in cls._instances:
            cls._instances[instance_name] = cls(**kwargs)
        return cls._instances[instance_name]

    @classmethod
    def drop_instance(cls, instance_name: Optional[str] = None) -> None:
        """从管理器中删除一个实例。"""
        if instance_name is None:
            instance_name = cls.__name__
        if instance_name in cls._instances:
            cls._instances.pop(instance_name)


#  用于管理全局“作用域”的类: 作用域（Scope）是 OpenMMLab 中用于区分不同代码库（如 mmdet, mmcls）的关键概念。
class DefaultScope(ManagerMixin):
    """一个管理当前默认作用域的类。
    # DefaultScope 保证在任何地方都能获取到当前应该在哪个代码库的上下文中。
    所有注册器实例都属于一个作用域。作用域是用于隔离不同项目模块的命名空间。
    例如, 'mmdet' 是 MMDetection 项目的作用域。
    `DefaultScope.get_current_instance()` 会返回当前的默认作用域。
    """

    def __init__(self, scope_name: str):
        self._scope_name = scope_name

    @property
    def scope_name(self):
        return self._scope_name

    @scope_name.setter
    def scope_name(self, scope_name: str):
        self._scope_name = scope_name

    @classmethod
    def get_current_instance(cls) -> 'DefaultScope':
        """获取当前的默认作用域实例。"""
        # 'DefaultScope' 是这个单例的默认名称
        return cls.get_instance('DefaultScope')

    @classmethod
    @contextmanager
    def overwrite_default_scope(cls, scope: str) -> Generator:
        """一个用于临时覆写默认作用域的上下文管理器。

        它常用于 `switch_scope_and_registry` 方法中，当构建一个模块时，
        如果配置中指定了 `_scope_`，就临时切换到那个作用域。
        """
        # 获取或创建一个默认的scope实例
        instance = cls.get_instance('DefaultScope', scope_name=scope)
        original_scope = instance.scope_name
        try:
            instance.scope_name = scope
            yield
        finally:
            # 无论如何，最终都会恢复到原始作用域
            instance.scope_name = original_scope


# 当 `Registry.get` 无法在已注册模块中找到key时，会尝试将key作为 "模块路径.对象名" 的形式（如 "torch.nn.Conv2d"）进行动态导入。
def get_object_from_string(identifier: str) -> Any:
    """通过完整的字符串路径动态导入一个对象（类、函数等）。"""
    if not isinstance(identifier, str):
        raise TypeError("Identifier must be a string.")
    try:
        module_path, obj_name = identifier.rsplit('.', 1)
        module = importlib.import_module(module_path)
        return getattr(module, obj_name)
    except (ImportError, AttributeError, ValueError) as e:
        raise ImportError(f"Could not import {identifier}.") from e



class Registry:
    """一个将字符串映射到类或函数的注册器。

    Args:
        name (str): 注册器名称。
        build_func (callable, optional): 用于从配置构建实例的函数。
            如果未指定，将使用 `build_from_cfg`。
        parent (:obj:`Registry`, optional): 父注册器。子注册器可以访问父注册器中注册的模块。
        scope (str, optional): 注册器的作用域。用于在层级结构中查找子注册器。
            默认为定义该注册器的包名，如'mmdet', 'mmcls'。
        locations (list[str]): 一个字符串列表，表示模块定义所在的路径。
            注册器会在需要时自动从这些路径导入模块。
    """

    def __init__(self,
                 name: str,
                 build_func: Optional[Callable] = None,
                 parent: Optional['Registry'] = None,
                 scope: Optional[str] = None,
                 locations: List[str] = []):
        self._name = name
        self._module_dict: Dict[str, Type] = dict()
        self._children: Dict[str, 'Registry'] = dict()
        self._scope = scope
        self._locations = locations
        self._imported = False  # 标志位，防止重复导入

        self.parent: Optional['Registry']
        if parent is not None:
            assert isinstance(parent, Registry)
            # 将自己添加到父注册器的子节点中
            parent._add_child(self)
            self.parent = parent
        else:
            self.parent = None

        # 构建函数的优先级: 1. 显式传入的 build_func 2. 继承自 parent.build_func 3. 默认的 build_from_cfg
        self.build_func: Callable
        if build_func is None:
            if self.parent is not None:
                self.build_func = self.parent.build_func
            else:
                self.build_func = build_from_cfg
        else:
            self.build_func = build_func

    def __len__(self) -> int:
        return len(self._module_dict)

    def __contains__(self, key: str) -> bool:
        """检查一个键是否已在注册器或其祖先中注册。"""
        return self.get(key) is not None

    def __repr__(self) -> str:
        """以表格形式美观地打印注册器内容。"""
        table = Table(title=f"Registry '{self.name}' in scope '{self.scope}'")
        table.add_column('Key', justify='left', style='cyan')
        table.add_column('Value (Module)', justify='left', style='green')

        # 对键进行排序，以便输出稳定
        for name, obj in sorted(self._module_dict.items()):
            table.add_row(name, str(obj))

        console = Console()
        # 使用capture来获取表格的字符串表示
        with console.capture() as capture:
            console.print(table, end='')

        repr_str = capture.get()
        if self._children:
            repr_str += '\nChildren:\n'
            for scope, child in self._children.items():
                repr_str += f"- {scope}: {child.name}\n"
        return repr_str

    # ----------------- 属性 -----------------
    @property
    def name(self) -> str:
        return self._name

    @property
    def scope(self) -> Optional[str]:
        return self._scope

    @property
    def module_dict(self) -> Dict[str, Type]:
        return self._module_dict

    @property
    def children(self) -> Dict[str, 'Registry']:
        return self._children

    @property
    def root(self) -> 'Registry':
        """获取根注册器。"""
        return self._get_root_registry()

    # ----------------- 核心方法 -----------------
    @contextmanager
    def switch_scope_and_registry(self, scope: Optional[str]) -> Generator:
        """临时切换全局作用域并获取对应的注册器。

        这是一个上下文管理器。在 `with` 语句块内，全局作用域会变为 `scope`。
        它会尝试在整个注册器树中找到与新作用域匹配的注册器并 `yield` 它。
        如果找不到，则 `yield` 当前注册器本身。
        退出 `with` 块后，全局作用域会恢复原状。

        Args:
            scope (str, optional): 目标作用域名称。

        Examples:
            >>> # 假设已定义根 MODELS, 以及子注册器 MMCLS_MODELS (scope='mmcls')
            >>> # 和 MMDET_MODELS (scope='mmdet')
            >>> print(DefaultScope.get_current_instance().scope_name)
            pylts
            >>> with MMDET_MODELS.switch_scope_and_registry(scope='mmcls') as registry:
            ...     print(f"Inside with: current scope is '{DefaultScope.get_current_instance().scope_name}'")
            ...     print(f"Returned registry is '{registry.name}' with scope '{registry.scope}'")
            ...
            Inside with: current scope is 'mmcls'
            Returned registry is 'mmcls_model' with scope 'mmcls'
            >>> print(DefaultScope.get_current_instance().scope_name)
            pylts
        """
        if scope is None:
            # 如果没有提供scope，则不需要切换，直接返回自己
            yield self
            return

        with DefaultScope.overwrite_default_scope(scope):
            # 获取根注册器，因为只有根知道所有的子注册器
            root = self._get_root_registry()
            # 从根开始搜索匹配scope的子注册器
            registry = root._search_child(scope)
            if registry is None:
                print_log(
                    f'无法在 "{root.name}" 注册树中找到作用域为 "{scope}" 的注册器。'
                    f'将使用当前注册器 "{self.name}" (作用域: "{self.scope}") 进行构建。',
                    level=logging.WARNING)
                registry = self
            yield registry

    def _get_root_registry(self) -> 'Registry':
        """递归地向上查找，返回根注册器。"""
        root = self
        while root.parent is not None:
            root = root.parent
        return root

    def _get_local_or_parent(self, key: str) -> Optional[Type]:
        """在本地或父级注册器中查找模块，避免递归调用get方法。"""
        # 首先在本地查找
        if key in self._module_dict:
            return self._module_dict[key]

        # 然后在父级中查找
        if self.parent is not None:
            return self.parent._get_local_or_parent(key)

        return None

    def get(self, key: str) -> Optional[Type]:
        """根据键获取注册的模块（类或函数）。

        查找逻辑:
        1. 如果 `key` 不包含作用域前缀 (如 'ResNet')：
           - 首先在当前注册器中查找。
           - 如果找不到，则逐级向上在父注册器中查找，直到根节点。
        2. 如果 `key` 包含作用域前缀 (如 'mmdet.FasterRCNN')：
           - 解析出作用域 `scope` 和真实键 `real_key`。
           - 如果 `scope` 是当前注册器的作用域，则只在当前注册器查找。
           - 如果 `scope` 是其他作用域，则从根注册器开始向下查找对应的子注册器，然后调用其 `get(real_key)`。
        3. 如果以上都找不到，会尝试将 `key` 视为完整路径（如 'torch.nn.Conv2d'）进行动态导入。

        Args:
            key (str): 注册模块的名称。

        Returns:
            Type or None: 找到的模块，或 None。

        Examples:
            >>> ROOT = Registry('root')
            >>> @ROOT.register_module()
            ... class A: pass
            >>> CHILD = Registry('child', parent=ROOT, scope='child')
            >>> @CHILD.register_module()
            ... class B: pass
            >>> SIBLING = Registry('sibling', parent=ROOT, scope='sibling')
            >>> @SIBLING.register_module()
            ... class C: pass
            >>> # 1. 在子注册器中获取自己的模块
            >>> CHILD.get('B') is B
            True
            >>> # 2. 在子注册器中获取父注册器的模块
            >>> CHILD.get('A') is A
            True
            >>> # 3. 通过作用域前缀，从一个子注册器获取兄弟注册器的模块
            >>> CHILD.get('sibling.C') is C
            True
        """
        # (代码中已有的静态类型检查和实现逻辑保持不变，这里仅添加注释)
        scope, real_key = self.split_scope_key(key)

        # 1. 触发懒加载（如果定义了 locations）
        self.import_from_location()

        obj_cls = None

        if scope is None or scope == self._scope:
            # 2.1 在自己这里找
            if real_key in self._module_dict:
                obj_cls = self._module_dict[real_key]
            # 2.2 如果没有指定scope，就去父节点找
            elif scope is None and self.parent:
                obj_cls = self.parent.get(real_key)
        else:  # fixed by @claude4
            root = self._get_root_registry()
            target_registry = root._search_child(scope)
            if target_registry is not None:
                # 在目标注册器中查找，避免无限循环
                obj_cls = target_registry._get_local_or_parent(real_key)

        if obj_cls is not None:
            return obj_cls

        # 3. 作为最后的手段，尝试动态导入
        try:
            obj_cls = get_object_from_string(key)
            return obj_cls
        except ImportError:
            # 如果动态导入也失败，说明真的找不到了
            pass

        # 最终找不到则返回None (KeyError将在build_from_cfg中抛出)
        return None

    @staticmethod
    def split_scope_key(key: str) -> Tuple[Optional[str], str]:
        """将 'scope.key' 格式的字符串拆分为 (scope, key)。"""
        # 这个辅助方法在 mmengine v0.6.0 之后被移入 Registry 内部，补上它
        if '.' not in key:
            return None, key
        scope, real_key = key.split('.', 1)
        return scope, real_key

    def _search_child(self, scope: str) -> Optional['Registry']:
        """深度优先搜索指定作用域的子注册器。"""
        if self._scope == scope:
            return self

        for child in self._children.values():
            registry = child._search_child(scope)
            if registry is not None:
                return registry
        return None

    def _add_child(self, registry: 'Registry') -> None:
        """添加一个子注册器。"""
        assert isinstance(registry, Registry)
        assert registry.scope is not None, '子注册器必须有作用域 (scope)'
        assert registry.scope not in self.children, \
            f"作用域 '{registry.scope}' 已存在于 '{self.name}' 注册器的子节点中"
        self.children[registry.scope] = registry

    def import_from_location(self) -> None:
        """从预定义位置 (self._locations) 自动导入模块以触发注册。"""
        if not self._imported:
            for loc in self._locations:
                try:
                    import_module(loc)
                    print_log(
                        f"已从 '{loc}' 自动导入模块，触发 {self.scope}::{self.name} 的注册。",
                        level=logging.DEBUG)
                except ImportError as e:
                    print_log(
                        f"从 '{loc}' 导入模块失败: {e}。请确保路径正确且依赖已安装。",
                        level=logging.WARNING)
            self._imported = True

    def build(self, cfg: dict, *args, **kwargs) -> Any:
        """构建一个实例。
        这是一个便捷方法，内部直接调用 `self.build_func`。

        Args:
            cfg (dict): 需要构建的配置字典，必须包含 `type` 键。

        Returns:
            Any: 构建好的对象。

        Examples:
            >>> MODELS = Registry('models')
            >>> @MODELS.register_module()
            ... class MyModel:
            ...     def __init__(self, name="default"):
            ...         self.name = name
            >>> model_instance = MODELS.build(dict(type='MyModel', name='test'))
            >>> model_instance.name
            'test'
        """
        # 将 registry=self 传入 build_func，这样 build_func 才知道去哪里找模块
        return self.build_func(cfg, *args, **kwargs, registry=self)

    def _register_module(self,
                         module: Type,
                         module_name: Optional[Union[str, List[str]]] = None,
                         force: bool = False) -> None:
        """注册模块的内部实现。"""
        if not callable(module):
            raise TypeError(f'要注册的 module 必须是可调用对象，但得到 {type(module)}')

        if module_name is None:
            module_name = module.__name__
        if isinstance(module_name, str):
            module_name = [module_name]

        for name in module_name:
            if not force and name in self._module_dict:
                existed_module = self._module_dict[name]
                raise KeyError(f"'{name}' 已在 '{self.name}' 注册器中注册 "
                               f"(位于 {existed_module.__module__})")
            self._module_dict[name] = module

    def register_module(
            self,
            name: Optional[Union[str, List[str]]] = None,
            force: bool = False,
            module: Optional[Type] = None) -> Union[type, Callable]:
        """注册一个模块（类或函数）。

        可以作为装饰器或普通函数使用。

        Args:
            name (str or list[str], optional): 注册用的名称。如果为None，则使用模块自身的 `__name__`。
                可以提供一个字符串列表来给同一个模块注册多个别名。
            force (bool): 如果为True，当名称已存在时，会强制覆盖。默认为False。
            module (type, optional): 要注册的模块。当作为普通函数调用时使用此参数。

        Returns:
            type or Callable: 返回被注册的模块本身。

        Examples:
            >>> # 1. 作为装饰器，使用默认名称
            >>> MODELS = Registry('models')
            >>> @MODELS.register_module()
            ... class AlexNet: pass

            >>> # 2. 作为装饰器，指定自定义名称或别名
            >>> @MODELS.register_module(name='vgg16')
            ... class VGG: pass
            >>> @MODELS.register_module(name=['MobileNetV2', 'mnetv2'])
            ... class MobileNet: pass

            >>> # 3. 作为普通函数使用
            >>> class GoogLeNet: pass
            >>> MODELS.register_module(module=GoogLeNet)

            >>> 'AlexNet' in MODELS and 'vgg16' in MODELS and 'mnetv2' in MODELS and 'GoogLeNet' in MODELS
            True
        """
        if not isinstance(force, bool):
            raise TypeError(f'force 必须是布尔值，但得到 {type(force)}')
        if not (name is None or isinstance(name, str) or is_seq_of(name, str)):
            raise TypeError('name 必须是 None, str, 或 str 序列')

        # 用法1: 作为普通函数调用 registry.register_module(module=MyClass)
        if module is not None:
            self._register_module(module=module, module_name=name, force=force)
            return module

        # 用法2: 作为装饰器 @registry.register_module()
        def _register(module_cls):
            self._register_module(module=module_cls, module_name=name, force=force)
            return module_cls

        return _register


if __name__ == '__main__':
    # --- 1. 创建层级注册器 ---
    # a. 创建一个根注册器，没有父节点，作用域为 'pylts' (默认值)
    MODELS = Registry('models', scope='pylts')

    # b. 创建2个子注册器，父节点都是 MODELS
    #    它们有各自的作用域，用于区分不同任务或库的模块
    DETECTORS = Registry('detectors', parent=MODELS, scope='pyldet')         # detectors
    CLASSIFIERS = Registry('classifiers', parent=MODELS, scope='pylcls')     # classifiers

    print("--- 注册器层级结构 ---")
    print(f"根注册器: {MODELS.name}, 作用域: {MODELS.scope}")
    print(f"子注册器: {list(MODELS.children.keys())}")
    print("-" * 20)

    # --- 2. 注册模块 ---
    @MODELS.register_module()
    class BaseModel:
        def __init__(self, name="base"):
            self.name = name

        def __repr__(self):
            return f"<{self.__class__.__name__}(name='{self.name}')>"


    @DETECTORS.register_module()
    class FasterRCNN:
        def __init__(self, backbone, neck):
            self.backbone = backbone
            self.neck = neck

        def __repr__(self):
            return f"<{self.__class__.__name__}(backbone={self.backbone}, neck={self.neck})>"


    @CLASSIFIERS.register_module(name='pyl_resnet')  # 使用自定义名称注册
    class ResNet:
        def __init__(self, depth=50):
            self.depth = depth

        def __repr__(self):
            return f"<{self.__class__.__name__}(depth={self.depth})>"


    # 打印注册器内容，验证注册是否成功
    print("\n--- 注册后各注册器内容 ---")
    print(MODELS)
    print(DETECTORS)
    print(CLASSIFIERS)
    print("-" * 20)

    # --- 3. 构建模块 ---
    print("\n--- 构建模块示例 ---")

    # a. 在子注册器(DETECTORS)中构建其自己注册的模块    TODO 可在配置文件中定义
    print(" 1. 构建 DETECTORS 中的 FasterRCNN... ")
    detector_cfg = dict(type='FasterRCNN',      # 参数也可以是嵌套的配置
                        backbone=dict(type='pyl_resnet'),  # 这里会向上找到 CLASSIFIERS 中的 pyl_resnet
                        neck=dict(type='BaseModel', name='fpn')  # 这里会向上找到 MODELS 中的 BaseModel
    )
    # backbone 和 neck 的 type 没有作用域前缀，所以会向上查找
    # 'resnet' 在 CLASSIFIERS 中找到，'BaseModel' 在 MODELS 中找到
    detector_cfg_scoped = dict(type='pyldet.FasterRCNN',  # 使用带作用域的类型更清晰：类似 scope.name 格式
                               backbone=dict(type='pylcls.pyl_resnet', depth=50),
                               neck=dict(type='BaseModel', name='fpn')  # 父模块不需要scope
    )

    # 使用 MODELS 这个根注册器来构建，它能看到所有子孙模块
    detector_instance = MODELS.build(detector_cfg_scoped)
    print(f"   构建成功: {detector_instance}\n")

    # b. 演示从子注册器访问父注册器模块
    print("2. 从 DETECTORS 构建父注册器中的 BaseModel...")
    base_model_instance = DETECTORS.build(dict(type='BaseModel', name='test'))
    print(f"   构建成功: {base_model_instance}\n")

    # c. 演示跨作用域构建 (从一个子注册器构建其兄弟注册器的模块)
    print("3. 从 DETECTORS 构建 CLASSIFIERS 中的 resnet...")
    # 必须提供 'scope.name' 格式的 type
    resnet_instance = DETECTORS.build(dict(type='pylcls.pyl_resnet', depth=34))
    print(f"   构建成功: {resnet_instance}\n")


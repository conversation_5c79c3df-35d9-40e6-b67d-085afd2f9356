# -*-coding:utf-8-*-
import numpy as np
import torch
print(torch.__version__)
import onnxruntime


def infer_onnx(session, img, output_shape=(1, 1000)):
    """
    use onnxruntime-gpu for inference;          # session.get_providers() known if Use GPU
    use IoBinding to accelerate the input copy to GPU.
    Args:
        session: onnxruntime.InferenceSession('model.onnx',
                                               providers=['CUDAExecutionProvider', 'CPUExecutionProvider'])
        img: the input as numpy_obj
        output_shape:

    Returns: pred.numpy()

    """
    io_binding = session.io_binding()                       # create a IoBinding obj
    input_tensor = onnxruntime.OrtValue.ortvalue_from_numpy(numpy_obj=img, device_type='cuda', device_id=0)
    io_binding.bind_input('input_name', input_tensor)       # banding to obj

    # 预分配输出并将其绑定到IoBinding对象
    output_tensor = onnxruntime.OrtValue.ortvalue_from_shape_and_type(output_shape, np.float32, 'cuda', 0)
    io_binding.bind_output('output_name', output_tensor)

    # 使用IoBinding运行模型
    session.run_with_iobinding(io_binding)
    pred = io_binding.get_outputs()[0].numpy()      # 获取输出结果

    return pred



def infer_onnx_gpu():
    device_name = 'cuda:0'  # or 'cpu'
    print(onnxruntime.get_device())
    print(onnxruntime.get_available_providers())

    if device_name == 'cpu':
        providers = ['CPUExecutionProvider']
    elif device_name == 'cuda:0':
        providers = ['CUDAExecutionProvider', 'CPUExecutionProvider']
    else:
        raise ValueError()
    # Create inference session
    # TODO 无法调用GPU推理，可能是cuda及cudnn版本不匹配
    onnx_model = onnxruntime.InferenceSession('run_50M_detect_foot_20210830_v1_960_960.onnx',
                                              providers=providers)
    # Create the input（这里的输入对应slowfast的输入）
    data = np.random.rand(1, 3, 960, 960).astype(np.float32)
    # Inference
    onnx_input = {onnx_model.get_inputs()[0].name: data}
    outputs = onnx_model.run(None, onnx_input)

    return outputs










if __name__ == '__main__':
    infer_onnx_gpu()
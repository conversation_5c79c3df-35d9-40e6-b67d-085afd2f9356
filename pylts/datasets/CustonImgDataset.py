# -*-coding:utf-8-*-
from __future__ import annotations

from pathlib import Path
from typing import List, Optional, Callable, Tuple, Any
from PIL import Image
import torch
from torch.utils.data import Dataset


class NonSupervision_ImageDataset(Dataset):
    """
    自定义图像数据集，支持任意嵌套目录结构
    适用于自监督学习场景，如SimSiam
    """

    # 支持的图像格式
    IMAGE_EXTENSIONS = {'.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.tif', '.webp'}

    def __init__(
            self,
            root_dir: str | Path,
            transform: Optional[Callable] = None,
            image_extensions: Optional[set] = None
    ) -> None:
        """
        初始化数据集

        Args:
            root_dir: 根目录路径
            transform: 图像变换函数
            image_extensions: 支持的图像格式扩展名集合
        """
        self.root_dir = Path(root_dir)
        self.transform = transform
        self.image_extensions = image_extensions or self.IMAGE_EXTENSIONS

        # 验证根目录存在
        if not self.root_dir.exists():
            raise FileNotFoundError(f"Root directory does not exist: {self.root_dir}")

        # 递归收集所有图像文件路径
        self.image_paths = self._collect_image_paths()

        if not self.image_paths:
            raise ValueError(f"No valid images found in {self.root_dir}")

    def _collect_image_paths(self) -> List[Path]:
        """
        递归收集所有支持格式的图像文件路径

        Returns:
            图像文件路径列表
        """
        image_paths = []

        # 使用pathlib的rglob进行递归搜索
        for ext in self.image_extensions:
            # 支持大小写不敏感的搜索
            pattern = f"*{ext}"
            image_paths.extend(self.root_dir.rglob(pattern))
            # 添加大写扩展名的搜索
            if ext != ext.upper():
                pattern_upper = f"*{ext.upper()}"
                image_paths.extend(self.root_dir.rglob(pattern_upper))

        # 去重并排序
        image_paths = sorted(set(image_paths))

        return image_paths

    def __len__(self) -> int:
        """返回数据集大小"""
        return len(self.image_paths)

    def __getitem__(self, idx: int) -> Tuple[Any, int]:
        """
        获取数据项

        Args:
            idx: 索引

        Returns:
            (image, label) 元组，label固定为0（自监督学习中被忽略）
        """
        if idx < 0 or idx >= len(self.image_paths):
            raise IndexError(f"Index {idx} out of range [0, {len(self.image_paths)})")

        img_path = self.image_paths[idx]

        try:
            # 打开图像并转换为RGB格式
            image = Image.open(img_path).convert('RGB')
        except (IOError, OSError) as e:
            raise RuntimeError(f"Cannot load image {img_path}: {e}") from e

        # 应用变换
        if self.transform is not None:
            image = self.transform(image)

        # 返回图像和 伪标签（自监督学习中标签会被忽略）
        return image, 0

    def get_image_path(self, idx: int) -> Path:
        """
        获取指定索引的图像路径

        Args:
            idx: 索引

        Returns:
            图像文件路径
        """
        if idx < 0 or idx >= len(self.image_paths):
            raise IndexError(f"Index {idx} out of range [0, {len(self.image_paths)})")

        return self.image_paths[idx]

    def __repr__(self) -> str:
        """字符串表示"""
        return (f"{self.__class__.__name__}("
                f"root_dir='{self.root_dir}', "
                f"num_images={len(self.image_paths)}, "
                f"transform={self.transform is not None})")


# 使用示例
if __name__ == "__main__":
    import torchvision.transforms as transforms

    # 定义数据增强
    augmentation = [
        transforms.RandomResizedCrop(224, scale=(0.2, 1.)),
        transforms.RandomApply([
            transforms.ColorJitter(0.4, 0.4, 0.4, 0.1)], p=0.8),
        transforms.RandomGrayscale(p=0.2),
        transforms.RandomApply([transforms.GaussianBlur(kernel_size=23)], p=0.5),
        transforms.RandomHorizontalFlip(),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406],
                             std=[0.229, 0.224, 0.225])
    ]

    # 假设你有simsiam的TwoCropsTransform
    # from simsiam.loader import TwoCropsTransform

    # 创建数据集
    train_dataset = NonSupervision_ImageDataset(
        root_dir="your_nested_dir",
        transform=transforms.Compose(augmentation)  # 或TwoCropsTransform(transforms.Compose(augmentation))
    )

    print(train_dataset)
    print(f"Found {len(train_dataset)} images")

    # 测试加载第一个样本
    if len(train_dataset) > 0:
        sample_image, sample_label = train_dataset[0]
        print(f"Sample image shape: {sample_image.shape if hasattr(sample_image, 'shape') else type(sample_image)}")
        print(f"Sample image path: {train_dataset.get_image_path(0)}")
# -*-coding:utf-8-*-
"""
#Useage-2：
对于输入的数据{np, tensor}，将其保存成图片 或 show出来
ImageSaver(numpy_data, 'numpy_data.png'， imshow=False)
"""
import os
from pathlib import Path
from PIL import Image
import numpy as np
import torch
import torchvision


class ImageSaver:
    def __init__(self, data, save_pth: str = None, imshow: bool = False):
        self.os_type = os.name  # 获取操作系统类型
        self.save_pth = save_pth
        self.imshow = imshow
        if save_pth is None:
            self.show = True  # 无保存路径，默认show出图片
        self.save_image(data, self.save_pth)

    def save_image(self, data, save_pth):
        # 尝试将数据转换为 NumPy 数组
        if isinstance(data, torch.Tensor):
            img = self.saver_tensor(data, save_pth)
        elif isinstance(data, np.ndarray):
            img = self.saver_np(data, save_pth)
        else:
            raise ValueError(f"@Moss: data type not in {torch.Tensor, np.ndarray}")

        if self.show:
            img.show()

    def format_np(self, data):
        # 确保数据类型为 uint8，并且值在 0-255 范围内
        if data.dtype != np.uint8:
            data = (data - data.min()) / (data.max() - data.min())  # 归一化到 [0, 1]
            data = (data * 255).astype(np.uint8)  # 转换为 uint8

        # 确保数据形状正确
        if len(data.shape) == 3:
            if data.shape[0] == 3:  # 如果是 (3, H, W) 格式
                data = np.transpose(data, (1, 2, 0))  # 转换为 (H, W, 3)
        else:
            raise ValueError(f'@Moss: check the Img shape {data.shape}')

        return data

    def saver_np(self, data: np, save_pth: str):
        """
        不show则保存图片，否则返回处理后的img<PIL>
        """
        data = self.format_np(data)
        # 使用 PIL 保存图片并展示
        img = Image.fromarray(data)
        if not self.show:
            img.save(save_pth)

        return img

    def saver_tensor(self, data: torch.Tensor, save_pth: str):
        if not self.show:
            assert Path(save_pth).absolute().exists(), f"@Moss: {Path(save_pth).absolute()} do not exist."
            torchvision.utils.save_image(data, save_pth)
        else:
            data = self.format_np(data.cpu().numpy())

        return data


if __name__ == '__main__':
    # example-2.1 保存 NumPy 数组
    numpy_data = np.random.rand(3, 224, 224).astype(np.float32)  # 示例数据，形状为 (3, 224, 224)
    ImageSaver(numpy_data, 'numpy_data.png')

    # 保存 PyTorch 张量
    tensor_data = torch.randn(3, 224, 224)  # 示例数据，形状为 (3, 224, 224)
    ImageSaver(tensor_data, 'tensor_image.png')

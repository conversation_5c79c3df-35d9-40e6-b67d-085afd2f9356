# -*-coding:utf-8-*-
from torchvision import datasets, transforms


# TODO 补全数据加载、数据预处理等预热过程 Ref：

class RepeatDataset:
    """A wrapper of repeated dataset.

    The length of repeated dataset will be ``times`` larger than the original
    dataset. This is useful when the data loading time is long but the dataset
    is small. Using RepeatDataset can reduce the data loading time between
    epochs.

    Args:
        dataset (dict): The config of the dataset to be repeated.
        times (int): Repeat times.
        test_mode (bool): Store True when building test or validation dataset.
            Default: False.
    """

    def __init__(self, dataset, test_mode=False):
        dataset['test_mode'] = test_mode
        # fixde by @Moss 2023/07/28
        self.dataset = PoseDataset(**dataset)           # use pose_datasets.py
        self.times = dataset['times']
        if dataset['dataset'].get('class_prob'):
            self.class_prob = dataset['dataset']['class_prob']

        self._ori_len = len(self.dataset)

    def __getitem__(self, idx):
        """Get data."""
        return self.dataset[idx % self._ori_len]

    def __len__(self):
        """Length after repetition."""
        return self.times * self._ori_len


# 图像数据预处理
def build_transform(is_train, args):
    from timm.data.constants import IMAGENET_DEFAULT_MEAN, IMAGENET_DEFAULT_STD
    from timm.data import create_transform

    resize_im = args.input_size > 32
    if is_train:
        # this should always dispatch to transforms_imagenet_train
        transform = create_transform(
            input_size=args.input_size,
            is_training=True,
            color_jitter=args.color_jitter,
            auto_augment=args.aa,
            interpolation=args.train_interpolation,
            re_prob=args.reprob,
            re_mode=args.remode,
            re_count=args.recount,
        )
        if not resize_im:
            # replace RandomResizedCropAndInterpolation with
            # RandomCrop
            transform.transforms[0] = transforms.RandomCrop(
                args.input_size, padding=4)
        return transform

    t = []
    if resize_im:
        size = int(args.input_size / args.eval_crop_ratio)
        t.append(
            transforms.Resize(size, interpolation=3),  # to maintain same ratio w.r.t. 224 images
        )
        t.append(transforms.CenterCrop(args.input_size))

    t.append(transforms.ToTensor())
    t.append(transforms.Normalize(IMAGENET_DEFAULT_MEAN, IMAGENET_DEFAULT_STD))
    return transforms.Compose(t)




if __name__ == '__main__':
    ann_file = '/root/share175/sport_datas/action_recongnition/base_skeleton/train/run50_videos/pkls_skeleton'
    methods_train= [dict(type='UniformSampleFrames', clip_len=48),  # clip_len
                    dict(type='PoseDecode'),
                    dict(type='PoseCompact', hw_ratio=1., allow_imgpad=True),
                    dict(type='Resize', scale=(-1, 64)),  # How ?
                    dict(type='RandomResizedCrop', area_range=(0.56, 1.0)),
                    dict(type='Resize', scale=(56, 56), keep_ratio=False),  # resize (56, 56)
                    dict(type='Flip', flip_ratio=0.5, left_kp=[1, 3, 5, 7, 9, 11, 13, 15], right_kp=[2, 4, 6, 8, 10, 12, 14, 16]),
                    dict(type='GeneratePoseTarget', with_kp=False, with_limb=True),
                    dict(type='FormatShape', input_format='NCTHW_Heatmap'),
                    dict(type='Collect', keys=['imgs', 'label'], meta_keys=[]),
                    dict(type='ToTensor', keys=['imgs', 'label'])]
    cfg = dict(type='RepeatDataset', ann_file=ann_file, times=10, class_prob=[1]*60 + [2]*60,
               dataset=dict(type='PoseDataset', ann_file=ann_file, split=0.6, pipeline=methods_train))

    dataset = RepeatDataset(cfg)
    print(dataset.__dict__)
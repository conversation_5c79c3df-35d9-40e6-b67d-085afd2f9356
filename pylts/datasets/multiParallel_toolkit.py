# -*-coding:utf-8-*-
"""
多线程/进程 并行工具包 - 改进版
修复了函数序列化问题，提供更稳定的多进程支持

多线程处理: 适合
    文件操作: 移动、复制、重命名大量文件: 建议32-64线程最佳 ———— 核心数 × 2-8
    数据处理: 批量处理图片、视频、文档
    网络请求: 并行API调用：              建议50-100线程最佳 ———— 核心数 × 5-12
    计算任务: CPU密集型并行计算：           建议8-16线程最佳 ———— 核心数 × 1-2
多进程 (适合CPU密集型):
    数据处理: 批量处理图片、视频、文档
    计算任务: CPU密集型并行计算 (建议使用CPU核心数)

Usage Example:
# 1. 简单多进程示例
from multiParallel_toolkit_aug import simple_parallel_process

def process_item(item):
    # 处理逻辑
    return result

results = simple_parallel_process(process_item, items, max_workers=4)

# 2. 带额外参数的多进程
from functools import partial
process_func = partial(process_item, param1=value1)
results = simple_parallel_process(process_func, items, max_workers=4)

"""
import functools
import time
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor, as_completed
from typing import Callable, Optional, List, Any
import os
import logging
from multiprocessing import Pool
from tqdm import tqdm
from functools import partial


# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class ParallelConfig:
    """并行处理配置类"""

    def __init__(
        self,
        max_workers: Optional[int] = None,
        executor_type: str = "thread",
        chunk_size: int = 1,
        show_progress: bool = True,
        timeout: Optional[float] = None,
    ):
        if max_workers is None:
            # 为多进程和多线程提供更合理的默认工作数
            if executor_type == "process":
                self.max_workers = os.cpu_count() or 1
            else:
                self.max_workers = min(32, (os.cpu_count() or 1) * 5)
        else:
            self.max_workers = max_workers

        self.executor_type = executor_type  # 'thread' or 'process'
        self.chunk_size = chunk_size
        self.show_progress = show_progress
        self.timeout = timeout


def simple_parallel_process(
    func: Callable,
    items: List[Any],
    max_workers: Optional[int] = None,
    use_process: bool = True,
    show_progress: bool = True,
) -> List[Any]:
    """
    简单的并行处理函数 - 直接使用multiprocessing.Pool，避免序列化问题

    Args:
        func: 要并行执行的函数
        items: 要处理的项目列表
        max_workers: 最大工作进程/线程数
        use_process: True使用多进程，False使用多线程
        show_progress: 是否显示进度条

    Returns:
        处理结果列表
    """
    if not items:
        return []

    max_workers = max_workers or os.cpu_count()

    if show_progress:
        mode = "多进程" if use_process else "多线程"
        print(
            f"开始使用{mode}处理 {len(items)} 个项目，使用 {max_workers} 个工作单元..."
        )

    if use_process:
        # 使用多进程
        with Pool(max_workers) as pool:
            if show_progress:
                results = list(tqdm(pool.imap(func, items), total=len(items)))
            else:
                results = pool.map(func, items)
    else:
        # 使用多线程
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            if show_progress:
                futures = [executor.submit(func, item) for item in items]
                results = []
                for future in tqdm(as_completed(futures), total=len(futures)):
                    try:
                        results.append(future.result())
                    except Exception as e:
                        logger.error(f"处理项目时发生错误: {e}")
                        results.append(None)
            else:
                futures = [executor.submit(func, item) for item in items]
                results = [future.result() for future in futures]

    if show_progress:
        print(f"处理完成！")

    return results


def simple_parallel_process_with_executor(
    func: Callable,
    items: List[Any],
    max_workers: Optional[int] = None,
    use_process: bool = True,
    show_progress: bool = True,
) -> List[Any]:
    """
    使用ProcessPoolExecutor/ThreadPoolExecutor的并行处理函数

    Args:
        func: 要并行执行的函数
        items: 要处理的项目列表
        max_workers: 最大工作进程/线程数
        use_process: True使用多进程，False使用多线程
        show_progress: 是否显示进度条

    Returns:
        处理结果列表
    """
    if not items:
        return []

    max_workers = max_workers or os.cpu_count()
    executor_class = ProcessPoolExecutor if use_process else ThreadPoolExecutor

    if show_progress:
        mode = "多进程" if use_process else "多线程"
        print(
            f"开始使用{mode}处理 {len(items)} 个项目，使用 {max_workers} 个工作单元..."
        )

    results = []
    with executor_class(max_workers=max_workers) as executor:
        # 提交所有任务
        futures = [executor.submit(func, item) for item in items]

        # 收集结果
        if show_progress:
            for future in tqdm(as_completed(futures), total=len(futures)):
                try:
                    results.append(future.result())
                except Exception as e:
                    logger.error(f"处理项目时发生错误: {e}")
                    results.append(None)
        else:
            for future in as_completed(futures):
                try:
                    results.append(future.result())
                except Exception as e:
                    logger.error(f"处理项目时发生错误: {e}")
                    results.append(None)

    if show_progress:
        print(f"处理完成！")

    return results


def parallel_process(config: Optional[ParallelConfig] = None):
    """
    改进的并行处理装饰器 - 增强错误处理
    """
    if config is None:
        config = ParallelConfig()

    def decorator(func: Callable):
        @functools.wraps(func)
        def wrapper(iterable, *args, **kwargs):
            # 如果是多进程，尝试使用简单的方式
            if config.executor_type == "process":
                items = list(iterable)
                if args or kwargs:
                    # 有额外参数时，创建partial函数
                    from functools import partial

                    process_func = (
                        partial(func, *args, **kwargs)
                        if args
                        else partial(func, **kwargs)
                    )
                    return simple_parallel_process(
                        process_func,
                        items,
                        config.max_workers,
                        use_process=True,
                        show_progress=config.show_progress,
                    )
                else:
                    return simple_parallel_process(
                        func,
                        items,
                        config.max_workers,
                        use_process=True,
                        show_progress=config.show_progress,
                    )
            else:
                # 线程处理使用原来的方式
                return _execute_parallel(func, iterable, config, *args, **kwargs)

        return wrapper

    return decorator


def _task_wrapper_with_args(item_and_args):
    """模块级别的任务包装函数，用于多进程序列化"""
    item, func, args, kwargs = item_and_args
    return func(item, *args, **kwargs)


def _execute_parallel(
    func: Callable, iterable, config: ParallelConfig, *args, **kwargs
):
    """执行并行处理的核心逻辑"""
    items = list(iterable)
    total_items = len(items)

    if total_items == 0:
        return []

    if config.show_progress:
        logger.info(
            f"开始使用 [{config.executor_type}] 模式并行处理 {total_items} 个项目..."
        )

    # 根据配置选择线程池或进程池
    executor_class = (
        ThreadPoolExecutor if config.executor_type == "thread" else ProcessPoolExecutor
    )

    results = []
    start_time = time.time()

    with executor_class(max_workers=config.max_workers) as executor:
        if args or kwargs:
            # 为多进程准备数据
            items_with_args = [(item, func, args, kwargs) for item in items]
            future_to_item = {
                executor.submit(_task_wrapper_with_args, item_args): item_args[0]
                for item_args in items_with_args
            }
        else:
            future_to_item = {executor.submit(func, item): item for item in items}

        completed_count = 0
        for future in as_completed(future_to_item, timeout=config.timeout):
            try:
                result = future.result()
                results.append(result)
                completed_count += 1

                if (
                    config.show_progress
                    and completed_count % max(1, total_items // 20) == 0
                ):
                    progress = (completed_count / total_items) * 100
                    logger.info(
                        f"进度: {progress:.1f}% ({completed_count}/{total_items})"
                    )

            except Exception as e:
                item = future_to_item[future]
                logger.error(f"处理项目 {item} 时发生错误: {e}")
                results.append(None)

    elapsed_time = time.time() - start_time
    if config.show_progress:
        logger.info(f"并行处理完成，耗时: {elapsed_time:.2f}秒")

    return results


# --- 便捷的预定义配置 ---


def fast_parallel(max_workers: int = 32):
    """(多线程) 快速并行配置，适合I/O密集型任务"""
    return ParallelConfig(
        max_workers=max_workers, executor_type="thread", show_progress=True
    )


def safe_parallel(max_workers: int = 8):
    """(多线程) 安全并行配置（较少线程）"""
    return ParallelConfig(
        max_workers=max_workers, executor_type="thread", show_progress=True
    )


def process_parallel(max_workers: Optional[int] = None):
    """(多进程) 并行配置，适合CPU密集型任务"""
    num_workers = max_workers or os.cpu_count() or 1
    return ParallelConfig(
        max_workers=num_workers, executor_type="process", show_progress=True
    )


# --- 使用示例 ---
if __name__ == "__main__":
    # --- 示例1: 简单多进程处理 ---
    print("=" * 20, "简单多进程处理示例", "=" * 20)

    def cpu_bound_task(number):
        """一个模拟CPU密集型计算的函数"""
        result = sum(i * i for i in range(number))
        return result

    # 一组需要大量计算的数字
    numbers_to_process = [10000, 20000, 30000, 40000] * 3

    # 使用简单的多进程处理: simple_parallel_process(函数名, 列表)
    results = simple_parallel_process(cpu_bound_task, numbers_to_process, max_workers=4)
    print(f"处理了 {len(results)} 个任务")
    print("\n")

    # --- 示例2: 带参数的多进程处理 ---
    print("=" * 20, "带参数的多进程处理示例", "=" * 20)
    items = [1, 2, 3, 4, 5] * 4

    def task_with_params(item, multiplier=1, offset=0):
        """带额外参数的任务函数"""
        return item * multiplier + offset

    # 使用partial创建 带参数的函数
    process_func = partial(task_with_params, multiplier=2, offset=10)
    # 带参数的多进程: simple_parallel_process(参数函数, 列表)
    results2 = simple_parallel_process(process_func, items, max_workers=2)
    print(f"带参数处理结果: {results2[:5]}...")  # 显示前5个结果
    print("\n")

    # --- 示例3: 多线程处理I/O密集型任务 ---
    print("=" * 20, "多线程I/O密集型任务示例", "=" * 20)

    def io_bound_task(url):
        """一个模拟文件下载的函数 (I/O密集型)"""
        time.sleep(0.1)  # 模拟网络延迟或文件读写耗时
        return f"Downloaded {url}"

    urls = [f"file_{i}.dat" for i in range(10)]

    # 使用多线程处理
    results3 = simple_parallel_process(
        io_bound_task, urls, use_process=False, max_workers=4
    )
    print(f"下载了 {len(results3)} 个文件")

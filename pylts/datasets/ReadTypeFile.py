import os
import asyncio
import concurrent.futures
from concurrent.futures import Thread<PERSON>oolExecutor, ProcessPoolExecutor
from pathlib import Path
import time
from typing import List, Set, Generator, Tuple
import threading
from queue import Queue
import multiprocessing as mp


class HighSpeedFileSearch:
    """高性能文件搜索类"""

    def __init__(self, max_workers: int = None):
        self.max_workers = max_workers or min(32, (os.cpu_count() or 1) + 4)

    def method1_os_walk_optimized(self, root_dir: str, extensions: Set[str]) -> List[str]:
        """方法1: 优化的os.walk() - 通常最快的单线程方法"""
        files = []
        extensions = {ext.lower() for ext in extensions}  # 预处理为小写

        try:
            for dirpath, dirnames, filenames in os.walk(root_dir):
                # 过滤隐藏目录以提高速度
                dirnames[:] = [d for d in dirnames if not d.startswith('.')]

                for filename in filenames:
                    if any(filename.lower().endswith(ext) for ext in extensions):
                        files.append(os.path.join(dirpath, filename))
        except (PermissionError, OSError):
            pass

        return files

    def method2_scandir_recursive(self, root_dir: str, extensions: Set[str]) -> List[str]:
        """方法2: 递归scandir() - 内存效率高"""

        def _scandir_recursive(path: str) -> Generator[str, None, None]:
            try:
                with os.scandir(path) as entries:
                    for entry in entries:
                        if entry.is_file(follow_symlinks=False):
                            if any(entry.name.lower().endswith(ext) for ext in extensions):
                                yield os.path.join(path, entry.name)
                        elif entry.is_dir(follow_symlinks=False) and not entry.name.startswith('.'):
                            yield from _scandir_recursive(entry.path)
            except (PermissionError, OSError):
                pass

        extensions = {ext.lower() for ext in extensions}
        return list(_scandir_recursive(root_dir))

    def method3_threading_walk(self, root_dir: str, extensions: Set[str]) -> List[str]:
        """方法3: 多线程os.walk() - 适合IO密集型"""
        files = []
        lock = threading.Lock()
        extensions = {ext.lower() for ext in extensions}

        def walk_directory(start_path: str):
            local_files = []
            try:
                for dirpath, dirnames, filenames in os.walk(start_path):
                    dirnames[:] = [d for d in dirnames if not d.startswith('.')]
                    for filename in filenames:
                        if any(filename.lower().endswith(ext) for ext in extensions):
                            local_files.append(os.path.join(dirpath, filename))
            except (PermissionError, OSError):
                pass

            with lock:
                files.extend(local_files)

        # 获取顶层目录列表
        try:
            with os.scandir(root_dir) as entries:
                top_dirs = [entry.path for entry in entries if entry.is_dir() and not entry.name.startswith('.')]

                # 检查根目录的文件
                for entry in entries:
                    if entry.is_file() and any(entry.name.lower().endswith(ext) for ext in extensions):
                        files.append(os.path.join(root_dir, entry.name))
        except (PermissionError, OSError):
            return files

        if not top_dirs:
            return files

        # 多线程处理子目录
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            executor.map(walk_directory, top_dirs)

        return files

    def method4_async_scandir(self, root_dir: str, extensions: Set[str]) -> List[str]:
        """方法4: 异步scandir() - 高并发"""

        async def scan_directory(path: str, semaphore: asyncio.Semaphore) -> List[str]:
            async with semaphore:
                return await asyncio.get_event_loop().run_in_executor(
                    None, self._sync_scan_directory, path, extensions
                )

        async def scan_all_directories():
            semaphore = asyncio.Semaphore(self.max_workers)
            directories_to_scan = [root_dir]
            all_files = []

            while directories_to_scan:
                current_dirs = directories_to_scan[:]
                directories_to_scan.clear()

                tasks = [scan_directory(dir_path, semaphore) for dir_path in current_dirs]
                results = await asyncio.gather(*tasks, return_exceptions=True)

                for result in results:
                    if isinstance(result, list):
                        files, subdirs = result
                        all_files.extend(files)
                        directories_to_scan.extend(subdirs)

            return all_files

        return asyncio.run(scan_all_directories())

    def _sync_scan_directory(self, path: str, extensions: Set[str]) -> List[List[str]]:
        """同步扫描单个目录"""
        files = []
        subdirs = []

        try:
            with os.scandir(path) as entries:
                for entry in entries:
                    if entry.is_file(follow_symlinks=False):
                        if any(entry.name.lower().endswith(ext) for ext in extensions):
                            files.append(entry.path)
                    elif entry.is_dir(follow_symlinks=False) and not entry.name.startswith('.'):
                        subdirs.append(entry.path)
        except (PermissionError, OSError):
            pass

        return [files, subdirs]

    def method5_multiprocessing(self, root_dir: str, extensions: Set[str]) -> List[str]:
        """方法5: 多进程处理 - 适合CPU密集型或大量文件"""
        # 获取顶层目录
        try:
            with os.scandir(root_dir) as entries:
                top_dirs = [entry.path for entry in entries if entry.is_dir() and not entry.name.startswith('.')]
                root_files = []

                # 处理根目录文件
                for entry in entries:
                    if entry.is_file() and any(entry.name.lower().endswith(ext) for ext in extensions):
                        root_files.append(os.path.join(root_dir, entry.name))
        except (PermissionError, OSError):
            return []

        if not top_dirs:
            return root_files

        # 将目录分块给不同进程
        num_processes = min(self.max_workers, len(top_dirs))
        chunk_size = max(1, len(top_dirs) // num_processes)
        dir_chunks = [top_dirs[i:i + chunk_size] for i in range(0, len(top_dirs), chunk_size)]

        # 准备参数
        args_list = [(chunk, extensions) for chunk in dir_chunks]

        all_files = root_files[:]

        with ProcessPoolExecutor(max_workers=num_processes) as executor:
            results = executor.map(_process_directory_chunk, args_list)
            for result in results:
                all_files.extend(result)

        return all_files

    def method6_multiprocessing_by_dir(self, root_dir: str, extensions: Set[str]) -> List[str]:
        """方法6: 按目录分配的多进程处理 - 更均衡的负载"""
        # 获取顶层目录
        try:
            with os.scandir(root_dir) as entries:
                top_dirs = [entry.path for entry in entries if entry.is_dir() and not entry.name.startswith('.')]
                root_files = []

                # 处理根目录文件
                for entry in entries:
                    if entry.is_file() and any(entry.name.lower().endswith(ext) for ext in extensions):
                        root_files.append(os.path.join(root_dir, entry.name))
        except (PermissionError, OSError):
            return []

        if not top_dirs:
            return root_files

        # 每个目录单独处理
        args_list = [(directory, extensions) for directory in top_dirs]

        all_files = root_files[:]

        with ProcessPoolExecutor(max_workers=self.max_workers) as executor:
            results = executor.map(_process_single_directory, args_list)
            for result in results:
                all_files.extend(result)

        return all_files


# 多进程需要的全局函数（必须在类外定义）
def _process_directory_chunk(args: Tuple[List[str], Set[str]]) -> List[str]:
    """多进程处理目录块的全局函数"""
    dirs_chunk, extensions = args
    local_files = []

    for directory in dirs_chunk:
        try:
            for dirpath, dirnames, filenames in os.walk(directory):
                dirnames[:] = [d for d in dirnames if not d.startswith('.')]
                for filename in filenames:
                    if any(filename.lower().endswith(ext) for ext in extensions):
                        local_files.append(os.path.join(dirpath, filename))
        except (PermissionError, OSError):
            continue

    return local_files


def _process_single_directory(args: Tuple[str, Set[str]]) -> List[str]:
    """处理单个目录的全局函数"""
    directory, extensions = args
    local_files = []

    try:
        for dirpath, dirnames, filenames in os.walk(directory):
            dirnames[:] = [d for d in dirnames if not d.startswith('.')]
            for filename in filenames:
                if any(filename.lower().endswith(ext) for ext in extensions):
                    local_files.append(os.path.join(dirpath, filename))
    except (PermissionError, OSError):
        pass

    return local_files


# 最优化的单一函数实现
def find_files_ultra_fast_multiprocess(root_directory: str, file_extensions: List[str], max_workers: int = None) -> \
List[str]:
    """
    多进程超高速文件搜索 - 适合大型目录

    Args:
        root_directory: 根目录路径
        file_extensions: 文件扩展名列表
        max_workers: 最大进程数，默认为CPU核心数

    Returns:
        匹配文件的完整路径列表
    """
    if not os.path.exists(root_directory):
        return []

    # 预处理扩展名
    extensions = {ext.lower() if ext.startswith('.') else f'.{ext.lower()}' for ext in file_extensions}
    max_workers = max_workers or min(mp.cpu_count(), 8)  # 限制最大进程数

    # 获取顶层目录
    try:
        with os.scandir(root_directory) as entries:
            top_dirs = [entry.path for entry in entries if entry.is_dir() and not entry.name.startswith('.')]
            root_files = []

            # 处理根目录文件
            for entry in entries:
                if entry.is_file() and any(entry.name.lower().endswith(ext) for ext in extensions):
                    root_files.append(os.path.join(root_directory, entry.name))
    except (PermissionError, OSError):
        return []

    if not top_dirs:
        return root_files

    # 准备多进程参数
    args_list = [(directory, extensions) for directory in top_dirs]
    all_files = root_files[:]

    try:
        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            results = executor.map(_process_single_directory, args_list)
            for result in results:
                all_files.extend(result)
    except Exception as e:
        print(f"多进程处理出错，回退到单线程: {e}")
        # 回退到单线程处理
        for directory in top_dirs:
            try:
                for dirpath, dirnames, filenames in os.walk(directory):
                    dirnames[:] = [d for d in dirnames if not d.startswith('.')]
                    for filename in filenames:
                        if any(filename.lower().endswith(ext) for ext in extensions):
                            all_files.append(os.path.join(dirpath, filename))
            except (PermissionError, OSError):
                continue

    return all_files


def find_files_ultra_fast(root_directory: str, file_extensions: List[str]) -> List[str]:
    """
    超高速文件搜索函数 - 推荐使用（单线程版本）

    Args:
        root_directory: 根目录路径
        file_extensions: 文件扩展名列表，如 ['.pkl', '.txt', '.csv']

    Returns:
        匹配文件的完整路径列表
    """
    if not os.path.exists(root_directory):
        return []

    # 预处理扩展名
    extensions = {ext.lower() if ext.startswith('.') else f'.{ext.lower()}' for ext in file_extensions}
    files = []

    try:
        # 使用os.walk，这通常是最快的方法
        for dirpath, dirnames, filenames in os.walk(root_directory):
            # 跳过隐藏目录以提高速度
            dirnames[:] = [d for d in dirnames if not d.startswith('.')]

            # 使用生成器表达式提高效率
            matched_files = (
                os.path.join(dirpath, filename)
                for filename in filenames
                if any(filename.lower().endswith(ext) for ext in extensions)
            )
            files.extend(matched_files)

    except (PermissionError, OSError) as e:
        print(f"访问目录时出错: {e}")

    return files


def find_files_threading(root_directory: str, file_extensions: List[str], max_workers: int = None) -> List[str]:
    """
    多线程文件搜索 - 适合IO密集型场景

    Args:
        root_directory: 根目录路径
        file_extensions: 文件扩展名列表
        max_workers: 最大线程数

    Returns:
        匹配文件的完整路径列表
    """
    if not os.path.exists(root_directory):
        return []

    extensions = {ext.lower() if ext.startswith('.') else f'.{ext.lower()}' for ext in file_extensions}
    max_workers = max_workers or min(32, (os.cpu_count() or 1) + 4)

    files = []
    lock = threading.Lock()

    def walk_directory(start_path: str):
        local_files = []
        try:
            for dirpath, dirnames, filenames in os.walk(start_path):
                dirnames[:] = [d for d in dirnames if not d.startswith('.')]
                for filename in filenames:
                    if any(filename.lower().endswith(ext) for ext in extensions):
                        local_files.append(os.path.join(dirpath, filename))
        except (PermissionError, OSError):
            pass

        with lock:
            files.extend(local_files)

    # 获取顶层目录列表
    try:
        with os.scandir(root_directory) as entries:
            top_dirs = [entry.path for entry in entries if entry.is_dir() and not entry.name.startswith('.')]

            # 检查根目录的文件
            for entry in entries:
                if entry.is_file() and any(entry.name.lower().endswith(ext) for ext in extensions):
                    files.append(os.path.join(root_directory, entry.name))
    except (PermissionError, OSError):
        return files

    if not top_dirs:
        return files

    # 多线程处理子目录
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        executor.map(walk_directory, top_dirs)

    return files


# 性能测试函数
def benchmark_all_methods(root_dir: str, extensions: List[str], iterations: int = 3):
    """性能测试所有方法"""
    print(f"测试目录: {root_dir}")
    print(f"搜索扩展名: {extensions}")
    print(f"测试轮数: {iterations}")
    print("=" * 80)

    searcher = HighSpeedFileSearch()
    extensions_set = {ext.lower() if ext.startswith('.') else f'.{ext.lower()}' for ext in extensions}

    methods = [
        ("os.walk优化版", searcher.method1_os_walk_optimized),
        ("递归scandir", searcher.method2_scandir_recursive),
        ("多线程walk", searcher.method3_threading_walk),
        ("多进程处理", searcher.method5_multiprocessing),
        ("多进程按目录", searcher.method6_multiprocessing_by_dir),
        ("单线程超快版", lambda d, e: find_files_ultra_fast(d, list(e))),
        ("多线程函数版", lambda d, e: find_files_threading(d, list(e))),
        ("多进程函数版", lambda d, e: find_files_ultra_fast_multiprocess(d, list(e))),
    ]

    results = {}

    for method_name, method_func in methods:
        times = []
        file_count = 0

        for i in range(iterations):
            start_time = time.perf_counter()
            try:
                if "函数版" in method_name:
                    files = method_func(root_dir, extensions)
                else:
                    files = method_func(root_dir, extensions_set)
                file_count = len(files)
            except Exception as e:
                print(f"{method_name} 出错: {e}")
                files = []
                file_count = 0

            end_time = time.perf_counter()
            times.append(end_time - start_time)

        avg_time = sum(times) / len(times)
        min_time = min(times)
        results[method_name] = min_time

        print(f"{method_name:<15}: 平均 {avg_time:.4f}s, 最快 {min_time:.4f}s, 找到 {file_count} 个文件")

    # 显示最快的方法
    fastest_method = min(results, key=results.get)
    print(f"\n🏆 最快方法: {fastest_method} ({results[fastest_method]:.4f}s)")

    return results


# 使用示例
if __name__ == "__main__":
    # 配置搜索参数
    search_directory = "."  # 修改为你的目标目录
    target_extensions = ['.pkl']  # 修改为你需要的扩展名

    print("=== 单线程快速搜索示例 ===")
    start = time.perf_counter()
    files = find_files_ultra_fast(search_directory, target_extensions)
    end = time.perf_counter()

    print(f"单线程找到 {len(files)} 个文件，耗时 {end - start:.4f} 秒")

    print("\n=== 多线程搜索示例 ===")
    start = time.perf_counter()
    files_mt = find_files_threading(search_directory, target_extensions)
    end = time.perf_counter()

    print(f"多线程找到 {len(files_mt)} 个文件，耗时 {end - start:.4f} 秒")

    print("\n=== 多进程搜索示例 ===")
    start = time.perf_counter()
    files_mp = find_files_ultra_fast_multiprocess(search_directory, target_extensions)
    end = time.perf_counter()

    print(f"多进程找到 {len(files_mp)} 个文件，耗时 {end - start:.4f} 秒")


    print(f"\n=== 性能对比测试 ===")
    if os.path.exists(search_directory):
        benchmark_all_methods(search_directory, target_extensions)
    else:
        print(f"目录 {search_directory} 不存在")

# 推荐的三个主要函数使用方式：

# 1. 单线程版本（通常最快，内存效率最高）：
# files = find_files_ultra_fast("/path/to/search", ['.pkl', '.txt'])

# 2. 多线程版本（IO密集型场景，如网络存储）：
# files = find_files_threading("/path/to/search", ['.pkl', '.txt'], max_workers=16)

# 3. 多进程版本（大型目录结构，CPU充足）：
# files = find_files_ultra_fast_multiprocess("/path/to/search", ['.pkl', '.txt'], max_workers=8)
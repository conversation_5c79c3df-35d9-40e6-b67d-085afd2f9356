# -*-coding:utf-8-*-
"""
对于大模型的训练，往往需要冻结部分权重
例如需要冻结：
# TODO
    def forward_features(self, x: torch.Tensor) -> torch.Tensor:
        x = self.patch_embed(x)
        if self.is_pos_embed:
            x = self._pos_embed(x)
        x = self.patch_drop(x)
        x = self.norm_pre(x)
        if self.grad_checkpointing and not torch.jit.is_scripting():
            x = checkpoint_seq(self.blocks, x)
        else:
            x = self.blocks(x)
        x = self.norm(x)
        return x
"""
import torch.nn as nn


# 递归冻结参数的函数
def choice_freeze_module(module, grad):
    """ grad=False表示冻结参数，True为解冻 """
    if isinstance(module, nn.Module):
        for param in module.parameters():
            param.requires_grad = grad
        for child in module.children():
            choice_freeze_module(child, grad)
    elif isinstance(module, nn.Parameter):
        module.requires_grad = grad





if __name__ == '__main__':

    # 冻结forward_features中调用的各个子模块的参数
    modules_to_freeze = [
        model.patch_embed,
        model.pos_embed if model.is_pos_embed else None,
        model.patch_drop,
        model.norm_pre,
        model.blocks,
        model.norm
    ]

    for module in modules_to_freeze:
        if module is not None:
            choice_freeze_module(module, grad=False)

    # 检查是否冻结成功
    for name, param in model.named_parameters():
        print(f"Layer: {name}, Requires Grad: {param.requires_grad}")


    # 解冻结
    for module in modules_to_freeze:
        if module is not None:
            choice_freeze_module(module, grad=True)

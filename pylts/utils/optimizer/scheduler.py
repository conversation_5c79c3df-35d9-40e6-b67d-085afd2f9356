# -*-coding:utf-8-*-
"""
常用调度器工厂
"""

import math
import torch
import torch.optim as optim
from torch.optim.lr_scheduler import _LRScheduler
import warnings


class WarmupCosineLR(_LRScheduler):
    """
    带预热的余弦退火学习率调度器。
    继承自 _LRScheduler 以更好地与 PyTorch 生态系统集成。
    """
    def __init__(self, optimizer, warmup_epochs, max_epochs, base_lr, min_lr=0, last_epoch=-1):
        self.warmup_epochs = warmup_epochs
        self.max_epochs = max_epochs
        self.base_lr = base_lr
        self.min_lr = min_lr
        super(WarmupCosineLR, self).__init__(optimizer, last_epoch)

    def get_lr(self):
        """根据当前 epoch 计算新的学习率"""
        if not self._get_lr_called_within_step:
            warnings.warn("To get the last learning rate computed by the scheduler, "
                          "please use `get_last_lr()`.", UserWarning)

        if self.last_epoch < self.warmup_epochs:
            # 预热阶段: 学习率从一个较小的值线性增加到 base_lr
            lr = self.base_lr * (self.last_epoch + 1) / self.warmup_epochs
        else:
            # 余弦退火阶段
            progress = (self.last_epoch - self.warmup_epochs) / (self.max_epochs - self.warmup_epochs)
            lr = self.min_lr + 0.5 * (self.base_lr - self.min_lr) * (1 + math.cos(math.pi * progress))
        
        # 确保学习率不会低于设定的最小值
        lr = max(lr, self.min_lr)

        # 为优化器中的每个参数组返回计算出的学习率
        return [lr for _ in self.optimizer.param_groups]


def get_scheduler(optimizer, scheduler_name, **kwargs):
    """
    学习率调度器工厂函数
        调度器名称支持: 'WarmCosine', 'step', 'multistep', 'exponential', 'plateau'.
    Args:
        optimizer: 优化器实例.
        scheduler_name (str): 调度器名称.
        **kwargs: 调度器特定的参数.

    Returns:
        torch.optim.lr_scheduler._LRScheduler: 学习率调度器实例.
    """
    scheduler_name = scheduler_name.lower()
    
    if scheduler_name == 'WarmCosine':
        # 使用我们自定义的带预热的余弦退火调度器
        return WarmupCosineLR(
            optimizer,
            warmup_epochs=kwargs.get('warmup_epochs', 5),
            max_epochs=kwargs.get('max_epochs', 300),
            base_lr=kwargs.get('base_lr', 1e-3),
            min_lr=kwargs.get('min_lr', 1e-6),
        )
    elif scheduler_name == 'step':
        # StepLR: 每隔 step_size 个 epoch，学习率乘以 gamma
        return optim.lr_scheduler.StepLR(
            optimizer,
            step_size=kwargs.get('step_size', 30),
            gamma=kwargs.get('gamma', 0.1),
        )
    elif scheduler_name == 'multistep':
        # MultiStepLR: 在指定的 milestones 处，学习率乘以 gamma
        return optim.lr_scheduler.MultiStepLR(
            optimizer,
            milestones=kwargs.get('milestones', [100, 200]),
            gamma=kwargs.get('gamma', 0.1),
        )
    elif scheduler_name == 'exponential':
        # ExponentialLR: 每个 epoch，学习率都乘以 gamma
        return optim.lr_scheduler.ExponentialLR(
            optimizer,
            gamma=kwargs.get('gamma', 0.95),
        )
    elif scheduler_name == 'plateau':
        # ReduceLROnPlateau: 当某个指标停止改进时，降低学习率
        # 注意: 这个调度器的 step 方法需要传入一个指标 (e.g., validation loss)
        return optim.lr_scheduler.ReduceLROnPlateau(
            optimizer,
            mode=kwargs.get('mode', 'min'),
            factor=kwargs.get('factor', 0.1),
            patience=kwargs.get('patience', 10),
            # 'verbose' 参数在旧版PyTorch中不受支持，已移除以保证兼容性
        )
    else:
        raise ValueError(f"未知的调度器: {scheduler_name}")


if __name__ == '__main__':
    # 创建一个虚拟模型和优化器用于演示
    model = torch.nn.Linear(10, 2)          # 在实际使用中, 请替换为您的真实模型和优化器
    optimizer_base = optim.AdamW(model.parameters(), lr=1e-3)  # 初始学习率会被调度器覆盖

    # ----------------------------------------------------------------
    # 重要的提示:
    # 在实际的训练循环中, 正确的调用顺序是:
    # optimizer.step()    # 首先更新模型权重
    # scheduler.step()    # 然后更新学习率
    # 以下示例仅为演示学习率如何变化, 并非完整的训练流程。
    # ----------------------------------------------------------------
    
    print("===== 1. 带预热的余弦退火调度器 (WarmupCosineLR) =====")

    cosine_scheduler = get_scheduler(optimizer_base,
                                     'WarmCosine',
        warmup_epochs=5, max_epochs=100, base_lr=1e-3, min_lr=1e-6
    )
    print("模拟训练10个epoch...")
    for epoch in range(10):
        print(f"Epoch {epoch+1:2d}, LR: {cosine_scheduler.get_last_lr()[0]:.7f}")
        # 在真实训练中, 这两步应该在 optimizer.step() 之后
        cosine_scheduler.step()

    print("===== 2. 步进学习率调度器 (StepLR) =====")
    step_scheduler = get_scheduler(
        optimizer_base, 'step', step_size=10, gamma=0.5
    )


    print("===== 3. 多步学习率调度器 (MultiStepLR) =====")
    multistep_scheduler = get_scheduler(
        optimizer_base, 'multistep', milestones=[10, 20], gamma=0.1
    )

    print("==== 4. 指数学习率调度器 (ExponentialLR) =====")
    exp_scheduler = get_scheduler(
        optimizer_base, 'exponential', gamma=0.9
    )
        
    print("===== 5. Plateau 学习率调度器 (ReduceLROnPlateau) =====")
    plateau_scheduler = get_scheduler(
        optimizer_base,
        'plateau',
        mode='min',
        factor=0.1,
        patience=3
    )
    # 模拟训练过程和验证损失
    print("模拟训练过程, 监控验证损失...")
    # 在真实训练中, loss 来自你的验证集
    val_losses = [1.0, 0.9, 0.95, 0.8, 0.82, 0.81, 0.8, 0.8, 0.7, 0.6]
    for epoch, loss in enumerate(val_losses):
        # 注意: 对于 ReduceLROnPlateau, .step() 需要传入一个监控指标
        # 并且正确的调用位置是在 validation loop 之后
        current_lr = optimizer_base.param_groups[0]['lr']
        print(f"Epoch {epoch+1:2d}, Val Loss: {loss:.2f}, LR: {current_lr:.7f}")
        plateau_scheduler.step(loss)

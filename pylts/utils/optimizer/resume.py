# -*-coding:utf-8-*-
"""
断点续传
"""
import torch


def save_checkpoint(model, optimizer, scheduler, epoch, batch_index, path):
    checkpoint = {
        'epoch': epoch,
        'batch_index': batch_index,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'scheduler_state_dict': scheduler.state_dict() if scheduler else None,
    }
    torch.save(checkpoint, path)
    return


def load_checkpoint(path, model, optimizer, scheduler):
    checkpoint = torch.load(path)
    model.load_state_dict(checkpoint['model_state_dict'])
    optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
    if checkpoint['scheduler_state_dict'] is not None:
        scheduler.load_state_dict(checkpoint['scheduler_state_dict'])

    return checkpoint['epoch'], checkpoint['batch_index']






if __name__ == '__main__':
    import torch
    import torch.nn as nn
    import torch.optim as optim
    from torch.utils.data import DataLoader

    resume = True
    save_interval = 5       # epoch < 100, set 1-5； epoch < 1k, set 10-50；

    # 定义模型、优化器和数据加载器
    model = ClassificationModel()
    optimizer = optim.SGD(model.parameters(), lr=0.001, momentum=0.9)
    scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=1, gamma=0.1)
    train_loader = DataLoader(LoadImagesAndLabels(), batch_size=32, shuffle=True)


    # 加载训练状态
    if resume:
        start_epoch, start_batch_index = load_checkpoint(check1point_path, model, optimizer, scheduler)
    else:
        start_epoch = 0
        start_batch_index = 0

    # 训练模型
    for epoch in range(start_epoch, num_epochs):
        for batch_index, (inputs, labels) in enumerate(train_loader, start=start_batch_index):
            # 训练步骤
            optimizer.zero_grad()
            outputs = model(inputs)
            loss = nn.CrossEntropyLoss()(outputs, labels)
            loss.backward()
            optimizer.step()

            # 每隔一定轮数保存训练状态
            if batch_index % save_interval == 0:
                save_checkpoint(model, optimizer, scheduler, epoch, batch_index, checkpoint_path)

        # 更新学习率
        scheduler.step()
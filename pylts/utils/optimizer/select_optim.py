# -*-coding:utf-8-*-
"""
优化器：
torch自带的：
    Adadelta ：通过维护一个累积的梯度平方来自动调整学习率，适用于处理稀疏数据。
    Adagrad ：通过为每个参数维护一个历史梯度平方的和来调整学习率，对非平稳目标效果较好，但学习率可能会下降过快。
    LBFGS ：是一种拟牛顿优化算法，利用有限的内存来近似海森矩阵，适用于小规模和中等规模的问题，对于大规模问题可能计算成本较高。
    RMSprop ：通过维护一个梯度平方的滑动平均来调整学习率，有效解决了 Adagrad 学习率下降过快的问题，常用于 RNN 等序列模型。
    Rprop ：不使用梯度的大小信息，只利用梯度的符号来更新权重，适用于某些特定类型的神经网络，如 MLP。

    SGD ：随机梯度下降，是最基本的优化算法之一，简单易实现，但在处理复杂优化问题时可能收敛较慢。
        ASGD ：平均随机梯度下降，通过在训练过程中平均模型参数来提高模型的泛化能力。
    Adam系列 ：结合了动量和自适应学习率的思想，是目前最常用的优化器之一，在多种场景下表现良好。
        AdamW ：在 Adam 的基础上对权重衰减进行了修正，使其与手动权重衰减更加一致，可改善模型的泛化性能。
        Adamax ：是 Adam 的变体，使用了无穷范数，可以看作是 Adam 的上限版本。
        NAdam ：Adam 的另一种变体，加入了动量的 Nesterov 加速梯度，可以加速收敛。
        RAdam ：可矩估计的自适应学习率优化器，对学习率的自适应调整更加平稳，提高了模型的训练稳定性。
        SparseAdam ：专门用于处理稀疏梯度的优化器，如在嵌入层中使用。
Usage:
    optimizer = optim.Adam(model.parameters(), lr=0.001)

Lion: @Moss：已将库(pip install lion-pytorch)合并至此文件         20250519
# from lion_pytorch import Lion
"""
from __future__ import annotations  # 推迟类型注解解析 py3.10后自带
from typing import Tuple, Callable
import torch
from torch.optim.optimizer import Optimizer
import torch.optim as optim


class Lion(Optimizer):
    def __init__(self, params,
                 lr: float = 1e-4,
                 betas: Tuple[float, float] = (0.9, 0.99),
                 weight_decay: float = 0.0,
                 use_triton: bool = False,
                 decoupled_weight_decay: bool = False,
                 ):
        assert lr > 0.
        assert all([0. <= beta <= 1. for beta in betas])

        self._init_lr = lr
        self.decoupled_wd = decoupled_weight_decay

        defaults = dict(
            lr=lr,
            betas=betas,
            weight_decay=weight_decay
        )

        super().__init__(params, defaults)

        self.update_fn = Lion.update_fn

        if use_triton:
            from lion_pytorch.triton import update_fn as triton_update_fn
            self.update_fn = triton_update_fn

    @staticmethod
    def update_fn(p, grad, exp_avg, lr, wd, beta1, beta2):
        """
        # update functions
        """
        # stepweight decay

        p.data.mul_(1. - lr * wd)

        # weight update

        update = exp_avg.clone().mul_(beta1).add(grad, alpha=1. - beta1).sign_()
        p.add_(update, alpha=-lr)

        # decay the momentum running average coefficient

        exp_avg.mul_(beta2).add_(grad, alpha=1. - beta2)

    @torch.no_grad()
    def step(
            self,
            closure: Callable | None = None
    ):

        loss = None
        if closure is not None:
            with torch.enable_grad():
                loss = closure()

        for group in self.param_groups:
            for p in filter(lambda px: px.grad is not None, group['params']):

                grad, lr, wd, beta1, beta2, state, decoupled_wd, init_lr = \
                    p.grad, group["lr"], group["weight_decay"], *group["betas"], self.state[p], self.decoupled_wd, self._init_lr

                # maybe decoupled weight decay

                if decoupled_wd:
                    wd /= init_lr

                # init state - exponential moving average of gradient values

                if len(state) == 0:
                    state['exp_avg'] = torch.zeros_like(p)

                exp_avg = state['exp_avg']

                self.update_fn(
                    p,
                    grad,
                    exp_avg,
                    lr,
                    wd,
                    beta1,
                    beta2
                )

        return loss


if __name__ == '__main__':
    # 创建一个简单的模型
    model = torch.nn.Linear(10, 1)

    # 实例化 Lion 优化器
    optimizer = Lion(model.parameters(), lr=1e-4, weight_decay=1e-2)
    # torch 自带优化器
    optimizer2 = optim.Adam(model.parameters(), lr=0.001)  # 例子2

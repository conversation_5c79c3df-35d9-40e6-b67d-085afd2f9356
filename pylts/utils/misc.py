# -*-coding:utf-8-*-
"""
* 杂(miscellaneous)：
1. 根据分类模型预测结果重新划分数据
"""
import re
from pathlib import Path
import shutil
from tqdm import tqdm


def split_data_byModel(det_pth: str, data_prelab: tuple, mod: str, *args):
    """
    根据分类模型预测结果，重新划分(拷贝或移动)数据,
    以预测结果作为文件夹名称
    *args: e.g. ['png'], 若有 同名数据的标签.xml ，可一起操作
    :param det_pth: dst_path
    :param data_prelab: (list(str,...), np.shape(n, top_k)), 数据路径, predict label by classify model
    :param mod: 'move' or 'copy' or 'Print' file, [move, copy] be used by shutil module

    """
    Path(det_pth).mkdir(exist_ok=True)

    data_pth, prelab = data_prelab      # 拆包 (data, lab)
    for lab in list(set(prelab.ravel())):
        print(lab.__class__)
    [(Path(det_pth) / Path(str(lab))).mkdir(exist_ok=True) for lab in list(set(prelab.ravel()))]  # mkdir

    Rec = str(Path(det_pth) / Path('Rec.md'))
    if mod in ['move', 'copy']:
        # 输入样本源路径，moved_dst,
        func_shutil = getattr(shutil, mod, f"@Moss: The func shutil has not attribute named {mod}.")
        print(f"Start {mod}:")
        for i, lab in tqdm(enumerate(prelab)):
            dst = str(Path(det_pth) / Path(str(lab[0])) / Path(data_pth[i]).name)
            func_shutil(data_pth[i], dst=dst)
            with open(Rec, 'a') as f:
                f.writelines(f"{dst}, top_k pred lab:{lab}\n")

            if args:  # 其他 同名数据.suffix 一起Move
                for suffix in args:
                    src_suffix = Path(data_pth[i]).with_suffix(suffix)
                    dst_suffix = str(Path(det_pth) / Path(str(lab[0])) / Path(data_pth[i]).with_suffix(suffix).name)
                    func_shutil(src=src_suffix, dst=dst_suffix)

    elif mod in ['Print', 'print']:
        for i, lab in enumerate(prelab):
            with open(Rec, 'a') as f:
                f.writelines(f"{data_pth[i]}, top_k pred lab:{lab}\n")
    else:
        raise ValueError(f"@Moss: {mod} not in ['Copy', 'Move']")

    return


def parma_results():
    file = '/root/share175/sport_datas/action_recongnition/base_skeleton/train/run50_videos/pkls_skeleton_infered/Rec.md'
    error = file.replace('Rec.md', 'Rec_error.md')
    with open(file, 'r') as f:
        data_lines = f.readlines()

    for line in data_lines:
        match1 = re.search(r"/(\d+)_", line)
        match2 = re.search(r"\[(\d+) \d+\]", line)
        if match2 and match1:
            if int(match1.group(1)) != int(match2.group(1)):
                with open(error, 'a') as f:
                    f.writelines(line)


if __name__ == '__main__':
    parma_results()

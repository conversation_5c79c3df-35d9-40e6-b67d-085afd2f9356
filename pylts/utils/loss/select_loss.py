# -*-coding:utf-8-*-
"""
分类模型-损失函数选择：
    FocalLoss、WeightedLabelSmoothingCrossEntropy、ClassBalancedFocalLoss、WeightedCrossEntropyWithPenalty、PolyLoss
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from sklearn.utils.class_weight import compute_class_weight



class FocalLoss(nn.Module):
    def __init__(self, alpha=None, gamma=2.0, reduction='mean'):
        super(FocalLoss, self).__init__()
        # 根据你的数据分布设置权重，不要求和为1
        self.alpha = torch.tensor([0.5, 0.6, 0.8, 1.0]) if alpha is None else alpha
        self.gamma = gamma
        self.reduction = reduction

    def forward(self, inputs, targets):
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = self.alpha[targets] * (1 - pt) ** self.gamma * ce_loss

        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()

        return focal_loss


class WeightedLabelSmoothingCrossEntropy(nn.Module):
    """带权重和标签平滑的交叉熵 BaseLine"""
    def __init__(self, weight, smoothing):
        super().__init__()
        self.weight = weight
        self.smoothing = smoothing
        self.confidence = 1.0 - smoothing

    def forward(self, pred, target):
        num_classes = pred.size(-1)

        # 标签平滑
        with torch.no_grad():
            true_dist = torch.zeros_like(pred)
            true_dist.fill_(self.smoothing / (num_classes - 1))
            true_dist.scatter_(1, target.data.unsqueeze(1), self.confidence)

        # 计算损失
        log_pred = F.log_softmax(pred, dim=-1)
        loss = -torch.sum(true_dist * log_pred, dim=-1)

        # 应用权重
        if self.weight is not None:
            weight_expanded = self.weight[target]
            loss = loss * weight_expanded

        return loss.mean()


class ClassBalancedFocalLoss(nn.Module):
    """类别平衡焦点损失"""
    def __init__(self, class_counts, beta, gamma):
        super().__init__()
        self.beta = beta
        self.gamma = gamma

        # 计算有效样本数权重
        effective_num = 1.0 - np.power(beta, class_counts)
        weights = (1.0 - beta) / effective_num
        self.weights = torch.FloatTensor(weights)

    def forward(self, pred, target):
        # 移动权重到正确的设备
        if self.weights.device != pred.device:
            self.weights = self.weights.to(pred.device)

        # 计算交叉熵
        ce_loss = F.cross_entropy(pred, target, reduction='none')
        pt = torch.exp(-ce_loss)

        # 应用权重和焦点项
        weight_t = self.weights[target]
        focal_loss = weight_t * (1 - pt) ** self.gamma * ce_loss

        return focal_loss.mean()


class WeightedCrossEntropyWithPenalty(nn.Module):
    """自定义加权交叉熵 + 类别特定惩罚: 可精确控制误判成本"""
    def __init__(self, class_weights, penalty_matrix):
        super().__init__()

        # 设置权重和惩罚矩阵
        if class_weights is None:
            class_weights = torch.tensor([0.5, 0.6, 0.8, 1.0])  # 给少数类更高权重
        self.class_weights = class_weights
        if penalty_matrix is None:
            print('TODO 需要根据类别数量和设定：')
            penalty_matrix = torch.tensor([
                [0.0, 1.0, 1.0, 1.0],  # 类别0的误判惩罚
                [5.0, 0.0, 0.5, 0.5],  # 类别1误判为0的严重惩罚
                [5.0, 0.5, 0.0, 0.5],  # 类别2误判为0的严重惩罚
                [10.0, 1.0, 1.0, 0.0]])  # 类别3误判为0的极严重惩罚
        self.penalty_matrix = penalty_matrix  # clsNumxclsNum矩阵，定义误判惩罚

    def forward(self, outputs, targets):
        # 基础加权交叉熵
        ce_loss = F.cross_entropy(outputs, targets, weight=self.class_weights)

        # 添加误判惩罚
        pred_probs = F.softmax(outputs, dim=1)
        penalty_loss = 0
        for i in range(len(targets)):
            true_class = targets[i]
            for pred_class in range(4):
                penalty_loss += pred_probs[i][pred_class] * self.penalty_matrix[true_class][pred_class]

        return ce_loss + 0.1 * penalty_loss / len(targets)


class PolyLoss(nn.Module):
    def __init__(self, epsilon, alpha):
        super().__init__()
        self.epsilon = epsilon
        self.alpha = alpha

    def forward(self, pred, target):
        ce_loss = F.cross_entropy(pred, target, reduction='none')
        pt = torch.exp(-ce_loss)

        # Poly损失公式
        poly_loss = self.alpha * (self.epsilon + 1.0) * (1 - pt) ** (self.epsilon + 1.0) + ce_loss

        return poly_loss.mean()

# ================================== the Follow is The Tool for loss-func select suggestion:


class LossWeightOptimizer:
    """损失函数权重优化工具类"""

    def __init__(self, class_counts, strategy='effective_number'):
        """
        Args:
            class_counts: 每个类别的样本数量，list或dict
            strategy: 权重计算策略
        """
        if isinstance(class_counts, dict):
            self.class_counts = np.array(list(class_counts.values()))
            self.class_names = list(class_counts.keys())
        else:
            self.class_counts = np.array(class_counts)
            self.class_names = list(range(len(class_counts)))

        self.total_samples = sum(self.class_counts)
        self.num_classes = len(self.class_counts)
        self.strategy = strategy

        self.strategies = ['inverse_frequency', 'effective_number', 'balanced', 'sqrt_inverse']

    def compute_weights(self):
        """根据策略计算权重"""
        if self.strategy == 'inverse_frequency':
            return self._inverse_frequency_weight()
        elif self.strategy == 'effective_number':
            return self._effective_number_weight()
        elif self.strategy == 'focal_loss':
            return self._focal_loss_weight()
        elif self.strategy == 'balanced':
            return self._balanced_weight()
        elif self.strategy == 'sqrt_inverse':
            return self._sqrt_inverse_weight()
        else:
            raise ValueError(f"Unknown strategy: {self.strategy}")

    def _inverse_frequency_weight(self):
        """逆频率权重（您当前使用的方法）"""
        weights = np.max(self.class_counts) / self.class_counts
        return torch.FloatTensor(weights)

    def _effective_number_weight(self, beta=0.9999):
        """有效样本数权重 - 推荐方法"""
        effective_num = 1.0 - np.power(beta, self.class_counts)
        weights = (1.0 - beta) / effective_num
        weights = weights / np.sum(weights) * self.num_classes  # 归一化
        return torch.FloatTensor(weights)

    def _balanced_weight(self):
        """sklearn风格的平衡权重"""
        weights = compute_class_weight(
            'balanced',
            classes=np.arange(self.num_classes),
            y=np.repeat(np.arange(self.num_classes), self.class_counts)
        )
        return torch.FloatTensor(weights)

    def _sqrt_inverse_weight(self):
        """平方根逆频率权重 - 温和的权重策略"""
        weights = np.sqrt(np.max(self.class_counts) / self.class_counts)
        return torch.FloatTensor(weights)

    def _focal_loss_weight(self, alpha=1.0, gamma=2.0):
        """焦点损失权重（动态权重）"""
        # 这里返回alpha参数，gamma在损失函数中使用
        frequencies = self.class_counts / self.total_samples
        weights = alpha * (1 - frequencies) ** gamma
        return torch.FloatTensor(weights)

    def analyze_weights(self):
        """分析不同权重策略的效果"""

        results = {}
        for strategy in self.strategies:
            self.strategy = strategy
            weights = self.compute_weights()
            results[strategy] = {
                'weights': weights.numpy(),
                'weight_ratio': weights.max() / weights.min(),
                'weight_variance': weights.var().item()
            }

        return results


class ImprovedLossFunctions:
    """改进的 损失函数合集"""

    @staticmethod
    def weighted_cross_entropy_with_smoothing(weight=None, smoothing=0.1):
        """带权重和标签平滑的交叉熵"""

        return WeightedLabelSmoothingCrossEntropy(weight, smoothing)

    @staticmethod
    def focal_loss(alpha=None, gamma=2.0, reduction='mean'):
        """焦点损失 - 自动处理类别不平衡"""

        return FocalLoss(alpha, gamma, reduction)

    @staticmethod
    def class_balanced_focal_loss(class_counts, beta=0.9999, gamma=2.0):
        """类别平衡焦点损失"""

        return ClassBalancedFocalLoss(class_counts, beta, gamma)

    @staticmethod
    def poly_loss(epsilon=1.0, alpha=1.0):
        """Poly损失 - 最新的损失函数"""

        return PolyLoss(epsilon, alpha)


class AdaptiveLossScheduler:
    """自适应损失调度器"""

    def __init__(self, initial_weights, adaptation_rate=0.1, min_weight=0.1):
        self.initial_weights = initial_weights
        self.current_weights = initial_weights.clone()
        self.adaptation_rate = adaptation_rate
        self.min_weight = min_weight
        self.class_errors = torch.zeros_like(initial_weights)

    def update_weights(self, pred, target):
        """根据预测错误率更新权重"""
        with torch.no_grad():
            # 计算每个类别的错误率
            pred_classes = pred.argmax(dim=1)
            for i in range(len(self.current_weights)):
                class_mask = (target == i)
                if class_mask.sum() > 0:
                    class_error = (pred_classes[class_mask] != target[class_mask]).float().mean()
                    self.class_errors[i] = (1 - self.adaptation_rate) * self.class_errors[i] + \
                                           self.adaptation_rate * class_error

            # 根据错误率调整权重
            error_weights = 1.0 + self.class_errors
            self.current_weights = self.initial_weights * error_weights
            self.current_weights = torch.clamp(self.current_weights, min=self.min_weight)

    def get_current_weights(self):
        return self.current_weights


def compare_loss_strategies(class_counts, num_samples=1000):
    """比较不同损失策略的效果"""

    # 创建模拟数据
    num_classes = len(class_counts)
    torch.manual_seed(42)

    # 模拟预测和真实标签
    pred = torch.randn(num_samples, num_classes)

    # 根据类别分布生成标签
    probabilities = np.array(class_counts) / sum(class_counts)
    target = torch.from_numpy(np.random.choice(num_classes, num_samples, p=probabilities))

    # 测试不同损失函数
    optimizer = LossWeightOptimizer(class_counts)

    results = {}

    # 1. 原始方法（逆频率）
    weights_inv = optimizer._inverse_frequency_weight()
    loss_inv = F.cross_entropy(pred, target, weight=weights_inv)
    results['Inverse Frequency (Your Current)'] = loss_inv.item()

    # 2. 有效样本数权重
    weights_eff = optimizer._effective_number_weight()
    loss_eff = F.cross_entropy(pred, target, weight=weights_eff)
    results['Effective Number'] = loss_eff.item()

    # 3. 焦点损失
    focal_loss_fn = ImprovedLossFunctions.focal_loss(gamma=2.0)
    loss_focal = focal_loss_fn(pred, target)
    results['Focal Loss'] = loss_focal.item()

    # 4. 类别平衡焦点损失
    cb_focal_fn = ImprovedLossFunctions.class_balanced_focal_loss(class_counts)
    loss_cb_focal = cb_focal_fn(pred, target)
    results['Class-Balanced Focal'] = loss_cb_focal.item()

    # 5. 带标签平滑的加权交叉熵
    weighted_smooth_fn = ImprovedLossFunctions.weighted_cross_entropy_with_smoothing(
        weight=weights_eff, smoothing=0.1
    )
    loss_weighted_smooth = weighted_smooth_fn(pred, target)
    results['Weighted + Label Smoothing'] = loss_weighted_smooth.item()

    return results


# 使用示例
def optimize_loss_function(class_counts, validation_loader=None):
    """损失函数优化主函数"""

    print("=== 损失函数优化分析 ===")

    # 1. 分析当前权重策略
    optimizer = LossWeightOptimizer(class_counts)
    weight_analysis = optimizer.analyze_weights()

    print("\n权重策略分析:")
    for strategy, info in weight_analysis.items():
        print(f"{strategy}:")
        print(f"  权重比例: {info['weight_ratio']:.2f}")
        print(f"  权重方差: {info['weight_variance']:.4f}")

    # 2. 比较损失函数效果
    print("\n损失函数效果比较:")
    loss_comparison = compare_loss_strategies(class_counts)
    for method, loss_val in loss_comparison.items():
        print(f"{method}: {loss_val:.4f}")

    # 3. 推荐最佳策略
    print("\n=== 优化建议 ===")

    # 计算不平衡程度
    imbalance_ratio = max(class_counts) / min(class_counts)

    if imbalance_ratio < 5:
        print("轻度不平衡 (比例 < 5:1)")
        print("推荐: 有效样本数权重 + 标签平滑")
        recommended_loss = ImprovedLossFunctions.weighted_cross_entropy_with_smoothing(
            weight=optimizer._effective_number_weight(),
            smoothing=0.1
        )
    elif imbalance_ratio < 20:
        print("中度不平衡 (比例 5:1 - 20:1)")
        print("推荐: 类别平衡焦点损失")
        recommended_loss = ImprovedLossFunctions.class_balanced_focal_loss(class_counts)
    else:
        print("严重不平衡 (比例 > 20:1)")
        print("推荐: 类别平衡焦点损失 + 重采样")
        recommended_loss = ImprovedLossFunctions.class_balanced_focal_loss(
            class_counts, beta=0.99, gamma=2.0
        )

    return recommended_loss, optimizer._effective_number_weight()



if __name__ == "__main__":
    # 示例：假设您有以下类别分布
    example_class_counts = [9041, 8328, 6260, 4910]  # 类别数量不平衡

    # 优化损失函数
    optimized_loss, recommended_weights = optimize_loss_function(example_class_counts)

    print(f"\n推荐权重: {recommended_weights}")
    print(f"您当前的权重计算方式可以改进为上述推荐方案")
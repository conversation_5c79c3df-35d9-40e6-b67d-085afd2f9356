# -*-coding:utf-8-*-
"""
# 1. 判断点与多边形的位置关系, 点是否在多边形内：is_point_in_polygon()
射线法的基本思想
    是从待判断的点出发，向任意方向发射一条射线，然后计算这条射线与多边形边界的交点数目。
    如果交点数为奇数，则该点在多边形内部；如果为偶数，则在外部。


cv2.pointPolygonTest() 其底层是优化后的射线法
    该函数能够计算点到轮廓的最短距离。如果点在轮廓外，返回负值；如果在内部，返回正值；如果在边上，返回零。
Usage:
    dist = cv2.pointPolygonTest(contour, (x, y), measureDist=True)


"""



class Point:
    # 定义2维点的输入方式, 及一些基础运算
    def __init__(self, x, y):
        self.x = x
        self.y = y

    @staticmethod
    def cross(A, B, C):
        # 计算叉积
        return (B.x - A.x) * (C.y - A.y) - (B.y - A.y) * (C.x - A.x)


def is_point_in_polygon(polygon: list, P: Point):
    """
    射线法：判断点P是否在多边形polygon内部, 具体步骤如下:
        选择一个方向，从点P出发画一条射线。
        遍历多边形的每条边，检查射线是否与该边相交。
        如果射线与边相交，且交点在射线的延长线上（即交点的x坐标大于点P的x坐标），则交点数加1。
        遍历完所有边后，如果交点数为奇数，则点P在多边形内部；如果为偶数，则在外部。

    :param polygon: 多边形的顶点列表，每个顶点是一个Point对象。
    :param P: 待判断的点，是一个Point对象。
    :return: 如果点P在多边形内部返回True，否则返回False。
    """
    wn = 0  # 交点数
    n = len(polygon)
    for i in range(n):
        A, B = polygon[i], polygon[(i + 1) % n]
        if A.y <= P.y < B.y and Point.cross(B, P, A) > 0:       # P 在AB 左侧
            wn += 1
        elif A.y > P.y >= B.y and Point.cross(B, P, A) < 0:
            wn -= 1

    return wn != 0  # 如果交点数不为0，则点在多边形内部



if __name__ == '__main__':

    # 多边形顶点坐标
    polygon = [Point(1, 0), Point(2, 0), Point(1, 2), Point(4, 5)]
    # 待判断点的坐标
    P = Point(1, 1)
    # 判断点与多边形的位置关系
    if is_point_in_polygon(polygon, P):
        print("Point P is inside the polygon.")
    else:
        print("Point P is outside the polygon.")

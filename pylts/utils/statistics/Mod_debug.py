"""
@Moss++自制debug工具：

#Useage-1：
1. @TryExcept(msg="") | with TryExcept(msg='')
(a) 在使用装饰器对类或函数进行包装时, 仅 msg = 'Jump' 时,才会跳出异常函数继续执行后续 | 否则 退出代码为 1
(b) with TryExcept 都会跳过包装的代码块

2. mem_analyze: False default 注意: 在代码无异常/报错时，才进行内存分析
pip install memory-profiler
简单实现：
from memory_profiler import profile
@profile
def your_func():
    return
python -m memory_profiler example.py -o profile_report.txt      # Todo: 保存到文件

"""
import contextlib
from memory_profiler import profile
import traceback
from pathlib import Path

import numpy as np
import torch
from PIL import Image
import torchvision
import os





class TryExcept(contextlib.ContextDecorator):
    """YOLOv8 TryExcept class.
    Usage: @TryExcept() decorator or 'with TryExcept():' context manager."""

    def __init__(self, msg='', verbose=True, mem_analyze=False):
        self.msg = msg
        self.verbose = verbose
        self.mem_analyze = mem_analyze

    def __enter__(self):
        pass

    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.msg == 'Jump' and exc_val:
            if self.verbose:
                self._print_error(exc_tb, "Jumping over error")
            return True
        elif self.msg is False:
            return False
        elif self.verbose and exc_val:
            self._print_error(exc_tb)
        return self.msg != False

    def __call__(self, func):
        if self.mem_analyze:
            func = profile(func)

        def wrapped(*args, **kwargs):
            if self.msg is False:
                return func(*args, **kwargs)
            try:
                return func(*args, **kwargs)
            except Exception as e:
                if self.verbose:
                    tb = traceback.extract_tb(e.__traceback__)[-1]
                    script_name = Path(tb.filename).name
                    print(f"{self.msg}{': ' if self.msg else ''}{e}")
                    print(f"Error in {script_name} line {tb.lineno}:\n\t{tb.line}")
                if self.msg == 'Jump':
                    # 如果 msg='Jump'，不重新抛出异常，继续执行后续代码
                    return None
                else:
                    raise

        return wrapped

    def _print_error(self, tb, prefix=None):
        tb_info = traceback.extract_tb(tb)[-1]
        script_name = Path(tb_info.filename).name
        error_msg = f"{prefix + ': ' if prefix else ''}{tb_info.line}"
        print(f"{self.msg}{': ' if self.msg else ''}{error_msg}")
        print(f"Error in {script_name} line {tb_info.lineno}:\n\t{tb_info.line}")


@TryExcept(msg="Jump", mem_analyze=False)
def func():
    a = [1] * (10 ** 6)
    b = [2] * (2 * 10 ** 7)
    f0 = 3 / 0
    f = a + b
    del b
    print('后续代码')
    return a


@TryExcept(msg="This func Ok and Analyze mem", mem_analyze=True)
def func2():
    a = [1] * (10 ** 6)
    b = [2] * (2 * 10 ** 7)
    f = a + b
    del b
    return a


if __name__ == '__main__':
    # example-1.1
    with TryExcept(msg="This func failure and Continue."):
        a = 1 / 0

    print(f'\n----------------------------------------------------------\n')

    # example-1.2
    # 主函数
    func()  # This func failure and Continue: division by zero
    func2()
    print('Okk.')

    # exit()



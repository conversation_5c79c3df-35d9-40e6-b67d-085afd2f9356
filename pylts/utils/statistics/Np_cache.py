"""
created by @Moss 2025.03.14
* np 数据存储在列表中(内存)造成的大文件溢出问题， 采用内存映射解决.

内存映射（Memory Mapping): 减少频繁的读写操作，从而提高性能。
减少系统调用开销：内存映射减少了频繁的 read() 和 write() 系统调用。
提高I/O效率：内存映射文件可以显著减少磁盘I/O操作次数，特别是在处理大文件时。
支持多进程共享：多个进程可以共享同一个文件映射，从而减少重复的磁盘读取
官方文档: https://numpy.org/doc/stable/reference/generated/numpy.memmap.html

* 支持自定义临时文件路径，默认存储一般在 /tmp
创建 MemoryMappedArrayStorage 对象，可以指定临时文件的存储目录。
使用 append 方法添加NumPy数组。
使用索引访问存储的数组。
在不需要存储对象时，调用 cleanup 方法清理临时文件，或让对象自动销毁时清理。

import torch
from tensordict import TensorDict
* pip install tensordict==0.2.1 匹配 torch 2.0.0
0.3.0及以上版本安装 依赖torch-2.6.0
其他版本：
Version Ref: https://github.com/pytorch/tensordict/tags
# 创建内存映射 TensorDict
mm_td = TensorDict({}, batch_size=[]).memmap_()
mm_td["data"] = torch.randn(1000, 1000)
应用场景
数据加载：用于数据集的加载和处理。
模型训练：简化模型训练过程中的数据管理。
分布式训练：支持分布式环境中的数据操作。

"""

import os
import numpy as np
from tempfile import NamedTemporaryFile




class MemoryMappedArrayStorage:
    def __init__(self, temp_dir=None):
        """
        初始化存储对象。
        :param temp_dir: 临时文件存储目录，如果为None则使用系统默认临时目录
        """
        if isinstance(temp_dir, str) and os.path.exists(temp_dir):
            self.temp_dir = temp_dir
        else:
            self.temp_dir = None
        # 创建临时文件，设置 delete=False 防止文件在关闭时自动删除
        self.temp_file = NamedTemporaryFile(dir=self.temp_dir, delete=False)
        # 存储每个内存映射数组的文件名、形状和数据类型
        self.array_info = []
        self.temp_files = []        # 存储临时文件对象

    def append(self, data):
        """
        将一个NumPy数组添加到存储中，使用内存映射技术。
        :param data: 要添加的NumPy数组 | Pytorch Tensor
        """
        shape, dtype = data.shape, data.dtype
        if isinstance(data, np.ndarray):
            # 创建内存映射数组 空
            mmap_array = np.memmap(self.temp_file.name, dtype=dtype, mode='w+', shape=shape)
            # 将数据复制到内存映射数组
            mmap_array[:] = data[:]
            mmap_array.flush()  # 确保数据写入磁盘

        self.array_info.append({'filename': self.temp_file.name, 'shape': shape, 'dtype': dtype})
        self.temp_files.append(self.temp_file)

    def __getitem__(self, index):
        """
        访问存储中的第index个数组。
        :param index: 数组的索引
        :return: 对应的内存映射数组
        """
        if index < 0 or index >= len(self.array_info):
            raise IndexError("索引超出范围")

        info = self.array_info[index]
        temp_filename = info.get('filename', 'tmp_path')
        t_dtype = info.get('dtype', 'uint8')
        t_shape = info.get('shape', 'tuple')

        return np.memmap(temp_filename, dtype=t_dtype, mode='r', shape=t_shape)

    def __len__(self):
        """
        获取存储中数组的数量。
        """
        return len(self.array_info)

    def __del__(self):
        """
        析构函数，用于在对象销毁时删除临时文件。
        """
        self.clean_up()

    def clean_up(self):
        """
        手动清理临时文件。
        """
        for info in self.array_info:
            try:
                os.remove(info['filename'])
                print(f"Deleted tmp file: {info['filename']}")
            except OSError as e:
                pass
                # print(f"Error deleting temporary file {info['filename']}: {e}")

        del self.array_info
        del self.temp_files


if __name__ == "__main__":
    # 创建存储对象，类似于check_img = []
    check_img = MemoryMappedArrayStorage()      # 指定临时文件存储目录,如temp_dir='./' 不指定则默认

    # 模拟存储多个1080p的NumPy数据
    for _ in range(8):
        # 创建随机数据
        input_np = np.random.randint(0, 256, (1080, 1920, 3), dtype=np.uint8)
        # 添加到存储
        check_img.append(input_np)

    # 使用方式: 访问存储的数组(同列表取值)
    for i in range(len(check_img)):
        array = check_img[i]
        print(f"访问第 {i} 个数组，形状为: {array.shape}")

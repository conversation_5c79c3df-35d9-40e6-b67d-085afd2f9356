# -*-coding:utf-8-*-

"""
# 计算(模型) 推理时间  2种方法
    测试方法1--time.time()秒：

    测速方法2--torch.cuda.Event毫秒

# 注意：在pytorch里面，程序的执行都是异步的。
torch.cuda.synchronize()
start = time.time()
result = model(input)
torch.cuda.synchronize()
end = time.time()
print('推理时间{}ms',end-start)     # 单位：秒

"""
import time
from functools import partial
import torch
import torch.nn as nn

from Mod_logger import init_logger



__all__ = ['mod_timer', 'use_timeInfer', 'use_torchEventInfer']


class mod_timer:
    """
    This is a Decorator of Timer module for func and code part:
    Usage:
    way1. @mod_timer
          def your_func():
            pass
    way2. with mod_timer() as t:
            your codes
    """
    def __init__(self, func=None):
        self.func = func
        self.logger = init_logger(log_level='INFO')  # logging.INFO
        self.start_code = None    # 片段
        self.end_code = None    # 片段

    def __call__(self, *args, **kwargs):
        _start = time.time()
        result = self.func(*args, **kwargs)
        _end = time.time()
        self.logger.info(f"{self.func.__name__} run {_end-_start:.4f} s.")

        return result

    def __get__(self, obj):
        return partial(self.__call__, obj)

    def __enter__(self):
        self.start_code = time.time()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.end_code = time.time()
        self.logger.info(f"This part run {self.end_code - self.start_code:.4f} s.")

    def __repr__(self):
        return f"<Mod_timer used for {self.func.__name__}>"


class _Net(nn.Module):
    """测试模型"""
    def __init__(self):
        super(_Net, self).__init__()
        self.conv = nn.Conv2d(in_channels=3, out_channels=1, kernel_size=3)
        self.relu = nn.ReLU()

    def forward(self, x):
        x = self.conv(x)
        x = self.relu(x)
        return x



def use_timeInfer(random_input, model, iters):
    """
    Use timer.time() get infer time.
    :param random_input:
    :param model: torch.Module.to(CUDA)
    :param iters: 重复计算的轮次,求平均
    :return: None
    """
    _time = torch.zeros(iters)  # 存储每轮iteration的时间
    with torch.no_grad():
        for i in range(iters):
            torch.cuda.synchronize()
            start = time.time()
            # 模型推理
            _ = model(random_input)
            torch.cuda.synchronize()
            end = time.time()

            _curr_time = end - start  # 计算时间
            _time[i] = _curr_time

    mean_time = _time.mean().item()
    logger = init_logger(log_level='INFO')  # logging.INFO
    logger.info(f'Time模块监测-推理时间： {mean_time*1000:.4f} 毫秒')      # time单位：s *1000 -> ms



def use_torchEventInfer(random_input, model, iters):
    starter, ender = torch.cuda.Event(enable_timing=True), torch.cuda.Event(enable_timing=True)

    _times = torch.zeros(iters)  # 存储每轮iteration的时间
    with torch.no_grad():
        for a_iter in range(iters):
            starter.record()
            # 模型推理
            _ = model(random_input)
            ender.record()
            torch.cuda.synchronize()  # torch是异步计算，需要同步GPU时间

            curr_time = starter.elapsed_time(ender)  # 计算时间
            _times[a_iter] = curr_time

    mean_time = _times.mean().item()
    logger = init_logger(log_level='INFO')  # logging.INFO
    logger.info(f"Torch.cuda.Event Monitor-Inference time: {mean_time:.4f} 毫秒, FPS: {(1000/mean_time):.2f} ")     # 毫秒




if __name__ == '__main__':

    model = _Net()
    device = torch.device("cuda:0")
    model.to(device)
    random_input = torch.randn(1, 3, 224, 224).to(device)

    # ------------------装饰器 Mod_Timer-----------------------------------------、
    with mod_timer():
        time.sleep(2)       # 秒


    # =================== GPU预热 =================================================
    starter, ender = torch.cuda.Event(enable_timing=True), torch.cuda.Event(enable_timing=True)
    starter.record()
    for _ in range(50):
        _ = model(random_input)
    ender.record()

    torch.cuda.synchronize()  # 同步GPU时间
    warm_up_time = starter.elapsed_time(ender)
    print("GPU warm up time:{:.2f}毫秒".format(warm_up_time))  # starter.elapsed_time单位：ms

    # ===========两种测速方法可选 =====================================
    iterations = 1000       # 重复计算的轮次,求平均
    # way1
    use_timeInfer(random_input, model, iterations)
    # way2
    use_torchEventInfer(random_input, model, iterations)

# -*-coding:utf-8-*-



def timm_download():
    import torch
    import timm

    # 1. 查看所有可用的 efficientnetv2_l 模型
    print(timm.list_models('efficientnetv2*'))  # 'efficientnetv2_l', 'efficientnetv2_l.in21k', 'efficientnetv2_l.in21k_ft_in1k' 等

    # 2. 选择模型
    #    模型名称: 'tf_efficientnetv2_l_in21k_ft_in1k'
    #    timm 库中，'tf_' 前缀表示权重是从 Google 官方的 TensorFlow 版本转换过来的，通常是性能最好的原始权重。
    # model_name = 'tf_efficientnetv2_l.in21k_ft_in1k'
    model_name = 'tf_efficientnetv2_s.in21k'

    print(f"正在创建并下载模型: {model_name}")
    # pretrained=True 会自动从网上下载预训练权重
    model = timm.create_model(model_name, pretrained=False)

    url = model.default_cfg['url']
    print(f"获取到模型权重URL: {url}")

    # 打印模型结构，确认加载成功
    # print(model)

    # 3. 将模型的状态字典 (state_dict) 保存为 .pth 文件
    #    这只保存了模型的权重，是推荐的做法
    save_path = f'pretrained_file/{model_name.replace(".", "_")}.pth'
    torch.save(model.state_dict(), save_path)

    #
    '''
    https://github.com/rwightman/pytorch-image-models/releases/download/v0.1-effv2-weights/tf_efficientnetv2_s_21k-6337ad01.pth
    https://github.com/rwightman/pytorch-image-models/releases/download/v0.1-effv2-weights/tf_efficientnetv2_m_21k-666ac058.pth
    https://github.com/rwightman/pytorch-image-models/releases/download/v0.1-effv2-weights/tf_efficientnetv2_l_21k-6337ad01.pth
    https://github.com/rwightman/pytorch-image-models/releases/download/v0.1-effv2-weights/tf_efficientnetv2_xl_in21k-7157352d.pth
    '''


    print(f"模型权重已成功保存到: {save_path}")

    # 如何加载你保存的权重？
    # 首先需要创建同样结构的模型（不加载预训练权重）
    new_model = timm.create_model(model_name, pretrained=False)
    new_model.load_state_dict(torch.load(save_path))
    # new_model.eval() # 切换到评估模式
    # print("模型权重加载成功!")


def torchvision_download():
    from torchvision.models import EfficientNet_V2_L_Weights
    from torchvision.models import efficientnet_v2_l

    # 注意：这里 weights=None
    model = efficientnet_v2_l(weights=None)         # # 首先，创建一个没有预训练权重的模型结构

    # 获取默认权重的 'enum' 对象
    weights_enum = EfficientNet_V2_L_Weights.DEFAULT

    # 打印出这个权重对应的下载 URL
    url = weights_enum.url
    print("EfficientNetV2-L 预训练权重的下载链接是:")
    print(url)
    print(f"use [weget or curl] download as follow:\n curl {url} -o {url.rsplit('/')[-1]}")


if __name__ == '__main__':
    # way:1
    # torchvision_download()

    # way:2
    timm_download()



# -*-coding:utf-8-*-
"""
GrabCut是一种经典的交互式图像分割算法，它只需要一个大致的矩形框就能分离前景和背景，而您的骨骼点信息可以为它提供更精确的引导。
思路：
利用人体框作为GrabCut的初始区域，再利用骨骼点告诉算法哪些区域“绝对是”前景，从而实现更精确的分割。
步骤：
选择基准帧作为最终的背景。
对于每个要合成的关键帧（起跳、滞空）：
提供初始矩形框： 直接使用您已有的人体框（Bounding Box）。
提供前景“种子”： 这是关键一步。根据您的骨骼点数据，生成一个“前景确定无疑”的标记。具体做法是：
将骨骼点连接起来，画成一个“火柴人”。
对这个“火柴人”线条进行**膨胀（Dilation）**操作，使其变成有一定宽度的粗线条。
将这个粗线条区域标记为“绝对前景”（cv2.GC_FGD in OpenCV）。
运行GrabCut： 将原图、人体框和我们制作的前景标记一起输入到GrabCut算法中。算法会自动迭代，计算出比简单背景减除法精确得多的前景蒙版。
合成图像：
使用GrabCut生成的高质量蒙版，从关键帧中抠出学生，粘贴到基准帧上。
优点：
完美利用了您已有的所有信息（人体框和骨骼点）。
效果远好于简单的背景减除，对背景和光照变化的容忍度更高。
相比于深度学习分割模型，计算量小很多。
缺点：
仍然可能在边缘处（如头发）处理得不够完美。
"""
import cv2
import numpy as np

# 定义COCO-17骨架的连接关系
# (这定义了哪些点应该被连接起来以形成四肢、躯干等)
connections = [
    (5, 6), (5, 11), (6, 12), (11, 12), (5, 7), (7, 9),
    (6, 8), (8, 10), (11, 13), (13, 15), (12, 14), (14, 16)
]


def create_mock_image(position_x, color, text):
    """创建一个简单的模拟图像用于演示。"""
    # 一个一致的背景
    img = np.full((300, 500, 3), (220, 200, 180), dtype=np.uint8)
    # 根据 (x_min, y_min, x_max, y_max) 绘制矩形
    cv2.rectangle(img, (position_x, 100), (position_x + 70, 100 + 130), color, -1)
    cv2.putText(img, text, (position_x + 5, 95), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
    return img


# --- 2. 算法核心 ---
def main(images, bboxes_xyxy, keypoints_list):
    # 选择最后一帧作为最终的背景图像
    final_image = images[-1].copy()

    # 我们将把前3帧的人物合成到最后一帧上
    for i in range(len(images) - 1):
        print(f"正在处理第 {i + 1} 帧并进行合成...")

        img_to_process = images[i]
        bbox_xyxy = bboxes_xyxy[i]
        keypoints = keypoints_list[i]

        # 将您的 (xmin, ymin, xmax, ymax) 格式的边界框转换为OpenCV所需的 (x, y, w, h) 格式
        x_min, y_min, x_max, y_max = bbox_xyxy
        x, y, w, h = x_min, y_min, x_max - x_min, y_max - y_min

        # 创建用于引导GrabCut算法的蒙版(mask)
        # 蒙版中的值: 0-确定背景, 1-确定前景, 2-可能背景, 3-可能前景
        mask = np.zeros(img_to_process.shape[:2], np.uint8)

        # 步骤1: 使用边界框初始化蒙版
        # 边界框外的所有东西默认为确定背景(0), 框内的所有东西初始化为可能前景(3)
        mask[y:y + h, x:x + w] = cv2.GC_PR_FGD

        # 步骤2: 使用骨骼点提供强前景引导
        # 将骨骼点像素标记为确定前景(1)
        # 使用粗线条来提供更强的信号
        line_thickness = 10  # 可以根据您的图像分辨率调整此值
        for conn in connections:
            pt1_idx, pt2_idx = conn
            # 对关键点的可见性进行安全检查
            if keypoints[pt1_idx] != (0, 0) and keypoints[pt2_idx] != (0, 0):
                pt1 = tuple(keypoints[pt1_idx])
                pt2 = tuple(keypoints[pt2_idx])
                cv2.line(mask, pt1, pt2, cv2.GC_FGD, thickness=line_thickness)

        for kp in keypoints:
            if kp != (0, 0):
                cv2.circle(mask, tuple(kp), radius=5, color=cv2.GC_FGD, thickness=-1)

        # 步骤3: 运行GrabCut算法
        bgdModel = np.zeros((1, 65), np.float64)
        fgdModel = np.zeros((1, 65), np.float64)

        grabcut_rect = (x, y, w, h)
        cv2.grabCut(img_to_process, mask, grabcut_rect, bgdModel, fgdModel,
                    iterCount=5,  # 5次迭代通常就足够了
                    mode=cv2.GC_INIT_WITH_MASK)  # 关键: 告诉GrabCut我们要使用自己提供的蒙版进行初始化

        # 步骤4: 创建用于合成的最终蒙版
        # GrabCut输出的蒙版包含0,1,2,3四种值。我们把确定前景(1)和可能前景(3)都当作最终的前景。
        final_mask = np.where((mask == cv2.GC_FGD) | (mask == cv2.GC_PR_FGD), 1, 0).astype('uint8')

        # 将单通道的蒙版扩展为3通道，以便与彩色图像进行相乘
        final_mask_3c = np.stack((final_mask,) * 3, axis=-1)

        # 步骤5: 合成图像
        # 从待处理的图像中提取前景
        foreground = img_to_process * final_mask_3c

        # 在最终图像中，将要粘贴前景的区域变黑（即“挖洞”）
        background_hole = final_image * (1 - final_mask_3c)

        # 将提取出的前景添加到最终图像中
        final_image = background_hole + foreground

    return final_image



if __name__ == '__main__':
    # 创建4张模拟图像来表示运动序列
    images = [
        create_mock_image(45, (0, 255, 0), "第1帧: 起跳"),
        create_mock_image(145, (0, 255, 255), "第2帧: 滞空"),
        create_mock_image(245, (0, 165, 255), "第3帧: 滞空"),
        create_mock_image(345, (0, 0, 255), "第4帧: 落地")
    ]

    # 您的边界框，格式为 (x_min, y_min, x_max, y_max)
    bboxes_xyxy = [
        (45, 95, 45 + 70, 95 + 130),
        (145, 95, 145 + 70, 95 + 130),
        (245, 95, 245 + 70, 95 + 130),
        (345, 95, 345 + 70, 95 + 130)
    ]

    # 您的关键点列表，每人17个点，遵循COCO-17格式。
    keypoints_list = [
        # 第1帧的关键点
        [(80, 110), (85, 105), (75, 105), (90, 108), (70, 108), (95, 130), (65, 130), (105, 160), (55, 160), (110, 190),
         (0, 0), (90, 180), (70, 180), (95, 200), (65, 200), (100, 215), (60, 215)],
        # 第2帧的关键点
        [(180, 110), (185, 105), (175, 105), (190, 108), (170, 108), (195, 130), (165, 130), (205, 150), (155, 150),
         (210, 170), (150, 170), (190, 175), (170, 175), (200, 195), (160, 195), (0, 0), (155, 210)],
        # 第3帧的关键点
        [(280, 110), (285, 105), (275, 105), (290, 108), (0, 0), (295, 130), (265, 130), (305, 160), (255, 160),
         (310, 190),
         (250, 190), (290, 180), (270, 180), (295, 200), (265, 200), (300, 215), (260, 215)],
        # 第4帧（作为背景基础）的关键点
        [(380, 110), (385, 105), (375, 105), (390, 108), (370, 108), (395, 130), (365, 130), (405, 160), (355, 160),
         (410, 190), (350, 190), (390, 180), (370, 180), (395, 200), (365, 200), (400, 215), (360, 215)]
    ]

    final_image = main(images, bboxes_xyxy, keypoints_list)

    # --- 3. 显示最终结果 ---
    print("\n处理完成！显示最终的合成图像。")
    cv2.imshow('Use GrabCut Act Moving', final_image)
    cv2.waitKey(0)
    cv2.destroyAllWindows()
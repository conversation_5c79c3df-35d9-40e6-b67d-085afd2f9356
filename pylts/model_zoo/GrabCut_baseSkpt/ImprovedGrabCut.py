# -*-coding:utf-8-*-
"""
优化的GrabCut人体分割合成算法 - 增强手脚分割版本 @claude4, Gemini2.5Pro
主要优化：
1. 扩展脚部区域检测
2. 增强手部区域预测和分割
3. 改进边界框扩展策略
4. 多阶段GrabCut处理
5. 形态学后处理
6. 添加手脚保护机制
7. 基于姿态估计的手脚区域扩展

GrabCut是一种经典的交互式图像分割算法，它只需要一个大致的矩形框就能分离前景和背景，而您的骨骼点信息可以为它提供更精确的引导。
思路：
利用人体框作为GrabCut的初始区域，再利用骨骼点告诉算法哪些区域“绝对是”前景，从而实现更精确的分割。
步骤：
选择基准帧作为最终的背景。
对于每个要合成的关键帧（起跳、滞空）：
提供初始矩形框： 直接使用您已有的人体框（Bounding Box）。
提供前景“种子”： 这是关键一步。根据您的骨骼点数据，生成一个“前景确定无疑”的标记。具体做法是：
将骨骼点连接起来，画成一个“火柴人”。
对这个“火柴人”线条进行**膨胀（Dilation）**操作，使其变成有一定宽度的粗线条。
将这个粗线条区域标记为“绝对前景”（cv2.GC_FGD in OpenCV）。
运行GrabCut： 将原图、人体框和我们制作的前景标记一起输入到GrabCut算法中。算法会自动迭代，计算出比简单背景减除法精确得多的前景蒙版。
合成图像：
使用GrabCut生成的高质量蒙版，从关键帧中抠出学生，粘贴到基准帧上。
优点：
完美利用了您已有的所有信息（人体框和骨骼点）。
效果远好于简单的背景减除，对背景和光照变化的容忍度更高。
相比于深度学习分割模型，计算量小很多。
缺点：
仍然可能在边缘处（如头发）处理得不够完美。


"""
import cv2
import numpy as np
import os
import math

# 定义COCO-17骨架的连接关系
connections = [
    (5, 6), (5, 11), (6, 12), (11, 12), (5, 7), (7, 9),
    (6, 8), (8, 10), (11, 13), (13, 15), (12, 14), (14, 16)
]

# 定义关键点索引
KEYPOINT_NAMES = [
    'nose', 'left_eye', 'right_eye', 'left_ear', 'right_ear',
    'left_shoulder', 'right_shoulder', 'left_elbow', 'right_elbow',
    'left_wrist', 'right_wrist', 'left_hip', 'right_hip',
    'left_knee', 'right_knee', 'left_ankle', 'right_ankle'
]

# 手腕和脚踝的索引
LEFT_WRIST, RIGHT_WRIST = 9, 10
LEFT_ANKLE, RIGHT_ANKLE = 15, 16
LEFT_ELBOW, RIGHT_ELBOW = 7, 8
LEFT_KNEE, RIGHT_KNEE = 13, 14


def calculate_distance(pt1, pt2):
    """计算两点之间的距离"""
    return math.sqrt((pt1[0] - pt2[0]) ** 2 + (pt1[1] - pt2[1]) ** 2)


def estimate_hand_regions(keypoints, img_shape):
    """基于手腕和肘部位置估计手部区域"""
    hand_regions = []

    # 处理左手
    if (LEFT_WRIST < len(keypoints) and LEFT_ELBOW < len(keypoints) and
            keypoints[LEFT_WRIST] != (0, 0) and keypoints[LEFT_ELBOW] != (0, 0)):

        wrist = keypoints[LEFT_WRIST]
        elbow = keypoints[LEFT_ELBOW]

        # 计算手臂方向向量
        arm_vector = (wrist[0] - elbow[0], wrist[1] - elbow[1])
        arm_length = calculate_distance(wrist, elbow)

        if arm_length > 0:
            # 估计手的长度（通常是前臂长度的0.3-0.4倍）
            hand_length = max(30, arm_length * 0.35)

            # 单位向量
            unit_vector = (arm_vector[0] / arm_length, arm_vector[1] / arm_length)

            # 估计手掌中心位置
            hand_center = (
                int(wrist[0] + unit_vector[0] * hand_length * 0.5),
                int(wrist[1] + unit_vector[1] * hand_length * 0.5)
            )

            # 确保在图像范围内
            hand_center = (
                max(0, min(img_shape[1] - 1, hand_center[0])),
                max(0, min(img_shape[0] - 1, hand_center[1]))
            )

            hand_regions.append({
                'center': hand_center,
                'wrist': wrist,
                'size': max(25, int(hand_length * 0.8)),
                'direction': unit_vector,
                'type': 'left_hand'
            })

    # 处理右手
    if (RIGHT_WRIST < len(keypoints) and RIGHT_ELBOW < len(keypoints) and
            keypoints[RIGHT_WRIST] != (0, 0) and keypoints[RIGHT_ELBOW] != (0, 0)):

        wrist = keypoints[RIGHT_WRIST]
        elbow = keypoints[RIGHT_ELBOW]

        # 计算手臂方向向量
        arm_vector = (wrist[0] - elbow[0], wrist[1] - elbow[1])
        arm_length = calculate_distance(wrist, elbow)

        if arm_length > 0:
            # 估计手的长度
            hand_length = max(30, arm_length * 0.35)

            # 单位向量
            unit_vector = (arm_vector[0] / arm_length, arm_vector[1] / arm_length)

            # 估计手掌中心位置
            hand_center = (
                int(wrist[0] + unit_vector[0] * hand_length * 0.5),
                int(wrist[1] + unit_vector[1] * hand_length * 0.5)
            )

            # 确保在图像范围内
            hand_center = (
                max(0, min(img_shape[1] - 1, hand_center[0])),
                max(0, min(img_shape[0] - 1, hand_center[1]))
            )

            hand_regions.append({
                'center': hand_center,
                'wrist': wrist,
                'size': max(25, int(hand_length * 0.8)),
                'direction': unit_vector,
                'type': 'right_hand'
            })

    return hand_regions


def estimate_foot_regions(keypoints, img_shape):
    """基于脚踝和膝盖位置估计脚部区域"""
    foot_regions = []

    # 处理左脚
    if (LEFT_ANKLE < len(keypoints) and LEFT_KNEE < len(keypoints) and
            keypoints[LEFT_ANKLE] != (0, 0)):

        ankle = keypoints[LEFT_ANKLE]

        # 估计脚的大小（基于人体比例）
        if keypoints[LEFT_KNEE] != (0, 0):
            knee = keypoints[LEFT_KNEE]
            leg_length = calculate_distance(ankle, knee)
            foot_length = max(35, leg_length * 0.25)
        else:
            foot_length = 40

        # 脚通常向下延伸
        foot_center = (
            int(ankle[0]),
            int(min(img_shape[0] - 1, ankle[1] + foot_length * 0.4))
        )

        foot_regions.append({
            'center': foot_center,
            'ankle': ankle,
            'length': int(foot_length),
            'width': max(20, int(foot_length * 0.6)),
            'type': 'left_foot'
        })

    # 处理右脚
    if (RIGHT_ANKLE < len(keypoints) and RIGHT_KNEE < len(keypoints) and
            keypoints[RIGHT_ANKLE] != (0, 0)):

        ankle = keypoints[RIGHT_ANKLE]

        # 估计脚的大小
        if keypoints[RIGHT_KNEE] != (0, 0):
            knee = keypoints[RIGHT_KNEE]
            leg_length = calculate_distance(ankle, knee)
            foot_length = max(35, leg_length * 0.25)
        else:
            foot_length = 40

        # 脚通常向下延伸
        foot_center = (
            int(ankle[0]),
            int(min(img_shape[0] - 1, ankle[1] + foot_length * 0.4))
        )

        foot_regions.append({
            'center': foot_center,
            'ankle': ankle,
            'length': int(foot_length),
            'width': max(20, int(foot_length * 0.6)),
            'type': 'right_foot'
        })

    return foot_regions


def validate_bbox(bbox, img_shape, expand_ratio=0.15):
    """验证并扩展边界框，特别关注手脚区域"""
    h, w = img_shape[:2]
    x_min, y_min, x_max, y_max = bbox

    # 计算扩展量
    bbox_w = x_max - x_min
    bbox_h = y_max - y_min
    expand_w = int(bbox_w * expand_ratio)
    expand_h = int(bbox_h * expand_ratio)

    # 扩展边界框，特别向下和左右扩展以包含手脚
    x_min = max(0, x_min - int(expand_w * 1.2))  # 左右多扩展一些
    y_min = max(0, y_min - expand_h)
    x_max = min(w, x_max + int(expand_w * 1.2))  # 左右多扩展一些
    y_max = min(h, y_max + int(expand_h * 1.5))  # 向下多扩展以确保包含脚部

    # 确保边界框有效
    x_max = max(x_min + 1, x_max)
    y_max = max(y_min + 1, y_max)

    return (x_min, y_min, x_max, y_max)


def create_enhanced_skeleton_mask(keypoints, img_shape, line_thickness=20):
    """创建增强的骨骼掩码，包含估计的手脚区域"""
    mask = np.zeros(img_shape[:2], dtype=np.uint8)

    # 绘制骨骼连接线
    for conn in connections:
        pt1_idx, pt2_idx = conn
        if (pt1_idx < len(keypoints) and pt2_idx < len(keypoints) and
                keypoints[pt1_idx] != (0, 0) and keypoints[pt2_idx] != (0, 0)):

            pt1 = tuple(int(p) for p in keypoints[pt1_idx])
            pt2 = tuple(int(p) for p in keypoints[pt2_idx])

            # 确保点在图像范围内
            if (0 <= pt1[0] < img_shape[1] and 0 <= pt1[1] < img_shape[0] and
                    0 <= pt2[0] < img_shape[1] and 0 <= pt2[1] < img_shape[0]):
                cv2.line(mask, pt1, pt2, 255, thickness=line_thickness)

    # 绘制关键点
    for i, kp in enumerate(keypoints):
        if kp != (0, 0):
            pt = tuple(int(p) for p in kp)
            if 0 <= pt[0] < img_shape[1] and 0 <= pt[1] < img_shape[0]:
                # 手腕和脚踝点使用更大的半径
                radius = 18 if i in [9, 10, 15, 16] else 10
                cv2.circle(mask, pt, radius=radius, color=255, thickness=-1)

    # 添加估计的手部区域
    hand_regions = estimate_hand_regions(keypoints, img_shape)
    for hand in hand_regions:
        center = hand['center']
        size = hand['size']
        wrist = tuple(int(p) for p in hand['wrist'])

        # 绘制手掌区域（椭圆）
        cv2.ellipse(mask, center, (size, int(size * 0.8)), 0, 0, 360, 255, -1)

        # 连接手腕到手掌中心
        if (0 <= center[0] < img_shape[1] and 0 <= center[1] < img_shape[0]):
            cv2.line(mask, wrist, center, 255, thickness=int(line_thickness * 0.8))

        # 添加手指区域（5个小椭圆）
        direction = hand['direction']
        finger_length = int(size * 0.6)
        finger_width = max(8, int(size * 0.2))

        for i in range(5):
            # 手指位置分布
            finger_angle = (i - 2) * 0.3  # -0.6 到 0.6 弧度

            # 旋转方向向量
            cos_a, sin_a = math.cos(finger_angle), math.sin(finger_angle)
            rotated_dir = (
                direction[0] * cos_a - direction[1] * sin_a,
                direction[0] * sin_a + direction[1] * cos_a
            )

            finger_tip = (
                int(center[0] + rotated_dir[0] * finger_length),
                int(center[1] + rotated_dir[1] * finger_length)
            )

            # 确保手指在图像范围内
            if (0 <= finger_tip[0] < img_shape[1] and 0 <= finger_tip[1] < img_shape[0]):
                cv2.ellipse(mask, finger_tip, (finger_width, int(finger_width * 1.5)),
                            int(math.degrees(math.atan2(rotated_dir[1], rotated_dir[0]))),
                            0, 360, 255, -1)

    # 添加估计的脚部区域
    foot_regions = estimate_foot_regions(keypoints, img_shape)
    for foot in foot_regions:
        center = foot['center']
        length = foot['length']
        width = foot['width']
        ankle = tuple(int(p) for p in foot['ankle'])

        # 绘制脚部区域（椭圆）
        cv2.ellipse(mask, center, (width, length), 0, 0, 360, 255, -1)

        # 连接脚踝到脚部中心
        if (0 <= center[0] < img_shape[1] and 0 <= center[1] < img_shape[0]):
            cv2.line(mask, ankle, center, 255, thickness=int(line_thickness * 0.6))

    return mask


def create_person_body_mask(keypoints, img_shape):
    """基于关键点创建人体轮廓掩码，包含手脚估计区域"""
    mask = np.zeros(img_shape[:2], dtype=np.uint8)

    # 获取有效关键点
    valid_points = []
    for kp in keypoints:
        if kp != (0, 0):
            pt = tuple(int(p) for p in kp)
            if 0 <= pt[0] < img_shape[1] and 0 <= pt[1] < img_shape[0]:
                valid_points.append(pt)

    # 添加估计的手脚区域点
    hand_regions = estimate_hand_regions(keypoints, img_shape)
    for hand in hand_regions:
        if (0 <= hand['center'][0] < img_shape[1] and 0 <= hand['center'][1] < img_shape[0]):
            valid_points.append(hand['center'])

    foot_regions = estimate_foot_regions(keypoints, img_shape)
    for foot in foot_regions:
        if (0 <= foot['center'][0] < img_shape[1] and 0 <= foot['center'][1] < img_shape[0]):
            valid_points.append(foot['center'])

    if len(valid_points) < 3:
        return mask

    # 创建凸包
    try:
        points = np.array(valid_points, dtype=np.int32)
        hull = cv2.convexHull(points)
        cv2.fillPoly(mask, [hull], 255)
    except:
        # 如果凸包失败，使用简单的点集填充
        for pt in valid_points:
            cv2.circle(mask, pt, 35, 255, -1)

    return mask


def apply_enhanced_grabcut(image, bbox, keypoints, debug=False):
    """应用增强的GrabCut算法，特别优化手脚分割"""
    # 验证并扩展边界框
    bbox = validate_bbox(bbox, image.shape, expand_ratio=0.25)
    x_min, y_min, x_max, y_max = bbox
    x, y, w, h = x_min, y_min, x_max - x_min, y_max - y_min

    if w <= 0 or h <= 0:
        print(f"无效的边界框: {bbox}")
        return None

    # 创建初始掩码
    mask = np.zeros(image.shape[:2], np.uint8)

    # 设置边界框内为可能前景
    mask[y:y + h, x:x + w] = cv2.GC_PR_FGD

    # 创建增强的骨骼掩码（包含手脚估计）
    skeleton_mask = create_enhanced_skeleton_mask(keypoints, image.shape, line_thickness=25)

    # 创建人体轮廓掩码（包含手脚估计）
    body_mask = create_person_body_mask(keypoints, image.shape)

    # 合并掩码：骨骼区域为确定前景
    mask[skeleton_mask > 0] = cv2.GC_FGD

    # 人体轮廓区域为可能前景（如果还不是确定前景的话）
    body_probable = (body_mask > 0) & (mask != cv2.GC_FGD)
    mask[body_probable] = cv2.GC_PR_FGD

    # 创建手脚保护掩码
    hand_foot_protection = skeleton_mask > 0

    if debug:
        # 保存调试图像
        debug_img = image.copy()
        cv2.rectangle(debug_img, (x, y), (x + w, y + h), (0, 255, 0), 2)

        # 绘制关键点
        for i, kp in enumerate(keypoints):
            if kp != (0, 0):
                pt = tuple(int(p) for p in kp)
                if 0 <= pt[0] < image.shape[1] and 0 <= pt[1] < image.shape[0]:
                    # 手腕和脚踝用特殊颜色标记
                    if i == LEFT_WRIST:
                        color = (255, 0, 255)  # 洋红色 - 左手腕
                    elif i == RIGHT_WRIST:
                        color = (255, 0, 128)  # 粉红色 - 右手腕
                    elif i == LEFT_ANKLE:
                        color = (0, 0, 255)  # 红色 - 左脚踝
                    elif i == RIGHT_ANKLE:
                        color = (0, 128, 255)  # 橙红色 - 右脚踝
                    else:
                        color = (255, 255, 0)  # 黄色 - 其他关键点

                    cv2.circle(debug_img, pt, 6, color, -1)
                    cv2.putText(debug_img, str(i), (pt[0] + 8, pt[1] - 8),
                                cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)

        # 绘制估计的手脚区域
        hand_regions = estimate_hand_regions(keypoints, image.shape)
        for i, hand in enumerate(hand_regions):
            cv2.circle(debug_img, hand['center'], hand['size'], (0, 255, 255), 2)
            cv2.putText(debug_img, f"Hand_{i}", (hand['center'][0] + 10, hand['center'][1]),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 2)

        foot_regions = estimate_foot_regions(keypoints, image.shape)
        for i, foot in enumerate(foot_regions):
            cv2.ellipse(debug_img, foot['center'], (foot['width'], foot['length']), 0, 0, 360, (255, 128, 0), 2)
            cv2.putText(debug_img, f"Foot_{i}", (foot['center'][0] + 10, foot['center'][1]),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 128, 0), 2)

        cv2.imwrite('debug_enhanced_bbox_keypoints_hands_feet.jpg', debug_img)
        cv2.imwrite('debug_enhanced_skeleton_mask_hands_feet.jpg', skeleton_mask)
        cv2.imwrite('debug_body_mask_hands_feet.jpg', body_mask)

    # 运行GrabCut - 多阶段处理
    try:
        bgdModel = np.zeros((1, 65), np.float64)
        fgdModel = np.zeros((1, 65), np.float64)

        # 第一阶段：粗分割
        cv2.grabCut(image, mask, (x, y, w, h), bgdModel, fgdModel,
                    iterCount=12, mode=cv2.GC_INIT_WITH_MASK)

        # 第二阶段：细化分割
        cv2.grabCut(image, mask, (x, y, w, h), bgdModel, fgdModel,
                    iterCount=10, mode=cv2.GC_EVAL)

        # 创建最终掩码
        final_mask = np.where((mask == cv2.GC_FGD) | (mask == cv2.GC_PR_FGD), 1, 0).astype('uint8')

        # 形态学后处理：填充小洞，去除小噪声
        kernel_close = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (7, 7))
        kernel_open = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))

        final_mask = cv2.morphologyEx(final_mask, cv2.MORPH_CLOSE, kernel_close)
        final_mask = cv2.morphologyEx(final_mask, cv2.MORPH_OPEN, kernel_open)

        # 确保手脚区域不被误删 - 强制保留估计的手脚区域
        final_mask[hand_foot_protection] = 1

        # 额外的手脚区域保护
        hand_regions = estimate_hand_regions(keypoints, image.shape)
        for hand in hand_regions:
            center = hand['center']
            size = hand['size']
            if (0 <= center[0] < image.shape[1] and 0 <= center[1] < image.shape[0]):
                cv2.circle(final_mask, center, size, 1, -1)

        foot_regions = estimate_foot_regions(keypoints, image.shape)
        for foot in foot_regions:
            center = foot['center']
            length, width = foot['length'], foot['width']
            if (0 <= center[0] < image.shape[1] and 0 <= center[1] < image.shape[0]):
                cv2.ellipse(final_mask, center, (width, length), 0, 0, 360, 1, -1)

        if debug:
            cv2.imwrite('debug_enhanced_final_mask_hands_feet.jpg', final_mask * 255)

            # 显示掩码覆盖的图像区域
            masked_img = image.copy()
            masked_img[final_mask == 0] = [0, 0, 255]  # 背景区域显示为红色
            cv2.imwrite('debug_masked_overlay_hands_feet.jpg', masked_img)

        return final_mask

    except Exception as e:
        print(f"GrabCut处理失败: {e}")
        return None


def smooth_mask_edges(mask):
    """平滑掩码边缘"""
    # 高斯模糊
    blurred = cv2.GaussianBlur(mask.astype(np.float32), (5, 5), 0)
    # 重新二值化
    return (blurred > 0.5).astype(np.uint8)


def main(images, bboxes_xyxy, keypoints_list, debug=False):
    """主处理函数"""
    if len(images) != len(bboxes_xyxy) or len(images) != len(keypoints_list):
        print("错误：图像、边界框和关键点数量不匹配")
        return None

    # 使用最后一帧作为背景
    final_image = images[-1].copy()

    # 处理前n-1帧
    for i in range(len(images) - 1):
        print(f"正在处理第 {i + 1} 帧...")

        img_to_process = images[i]
        bbox_xyxy = bboxes_xyxy[i]
        keypoints = keypoints_list[i]

        print(f"  边界框: {bbox_xyxy}")
        print(f"  关键点数量: {len(keypoints)}")

        # 统计有效关键点
        valid_kps = sum(1 for kp in keypoints if kp != (0, 0))
        print(f"  有效关键点数量: {valid_kps}")

        # 应用增强的GrabCut
        mask = apply_enhanced_grabcut(img_to_process, bbox_xyxy, keypoints, debug=debug)

        if mask is None:
            print(f"  第 {i + 1} 帧处理失败，跳过")
            continue

        # 检查掩码是否有效
        if np.sum(mask) == 0:
            print(f"  第 {i + 1} 帧生成的掩码为空，跳过")
            continue

        # 平滑掩码边缘
        mask = smooth_mask_edges(mask)

        # 扩展掩码到3通道
        mask_3c = np.stack((mask,) * 3, axis=-1)

        # 合成图像 - 使用更柔和的混合
        foreground = img_to_process * mask_3c
        background_mask = 1 - mask_3c
        final_image = final_image * background_mask + foreground

        print(f"  第 {i + 1} 帧处理完成，掩码像素数: {np.sum(mask)}")

        if debug:
            cv2.imwrite(f'debug_frame_{i + 1}_enhanced_result_hands_feet.jpg', final_image)

    return final_image


if __name__ == '__main__':
    imgs_dir = f"Demo/GrabCut_StandJump/images"
    # 图像路径
    paths = [f'{imgs_dir}/0.png',
             f'{imgs_dir}/1.png',
             f'{imgs_dir}/2.png',
             f'{imgs_dir}/3.png']         # 最后1张图作为背景

    # 检查文件是否存在
    for path in paths:
        if not os.path.exists(path):
            print(f"警告：文件 {path} 不存在")

    # 加载图像
    images = []
    for i, path in enumerate(paths):
        if os.path.exists(path):
            img = cv2.imread(path)
            if img is not None:
                images.append(img)
                print(f"成功加载图像 {path}, 尺寸: {img.shape}")
            else:
                print(f"无法读取图像 {path}")
        else:
            print(f"文件不存在: {path}")

    if len(images) < 2:
        print("错误：至少需要2张图像")
        exit(1)

    # 边界框数据 (x_min, y_min, x_max, y_max)
    bboxes_xyxy = [
        (241, 64, 498, 938),
        (346, 360, 944, 943),
        (757, 159, 1335, 764),
        (1257, 540, 1620, 913)
    ]

    # 关键点数据
    keypoints_list = [
        # 第0帧
        [[394.9726, 250.1283], [398.8198, 233.7169], [387.5718, 237.1851],
         [368.1991, 220.3248], [344.5691, 230.6742], [370.7738, 249.3056],
         [299.5626, 293.4601], [411.7527, 166.7567], [329.6754, 234.9580],
         [355.8163, 108.8435], [317.4662, 132.6699], [414.5693, 499.1006],
         [351.5877, 528.3154], [424.7015, 664.1374], [376.0468, 703.9518],
         [419.8526, 778.7982], [375.1996, 862.8329]],

        # 第1帧
        [[753.3230, 464.9225], [752.8236, 448.5172], [745.7919, 454.6928],
         [713.5446, 433.8021], [696.8246, 452.0276], [681.5557, 448.4093],
         [663.1916, 512.8766], [761.2061, 509.6880], [731.4813, 634.3796],
         [856.3715, 560.9849], [858.5312, 650.4448], [548.6055, 612.4923],
         [529.3511, 652.5026], [544.7068, 747.5195], [526.6090, 813.3156],
         [430.7011, 795.0632], [394.3286, 877.1793]],

        # 第2帧
        [[1192.9907, 355.5168], [1198.7710, 337.2881], [1195.7441, 342.6608],
         [1166.6089, 314.1758], [1156.5331, 334.9252], [1112.8114, 305.2485],
         [1123.6776, 362.0792], [1200.9990, 290.5823], [1246.1855, 377.4626],
         [1282.8875, 239.0076], [1309.7968, 263.7540], [966.6715, 517.0914],
         [956.0975, 559.8833], [1008.3260, 680.6944], [992.3414, 742.9705],
         [843.5882, 633.3093], [831.2365, 664.1553]],

        # 第3帧（背景帧）
        [[1549.8767, 634.9720], [1553.0049, 621.1978], [1555.6289, 627.7741],
         [1532.0343, 594.2391], [1536.7598, 617.0854], [1478.4941, 586.8657],
         [1517.1722, 647.9963], [1478.5627, 670.7999], [1533.7025, 749.3979],
         [1505.5728, 732.7801], [1591.2924, 827.8583], [1309.0979, 720.4781],
         [1334.0283, 766.0050], [1463.1464, 715.9559], [1472.9547, 754.7137],
         [1422.8563, 820.7651], [1416.3677, 866.9797]]
    ]

    # 确保数据长度一致
    min_len = min(len(images), len(bboxes_xyxy), len(keypoints_list))
    images = images[:min_len]
    bboxes_xyxy = bboxes_xyxy[:min_len]
    keypoints_list = keypoints_list[:min_len]

    print(f"将处理 {len(images)} 张图像")
    print("图层合成优先级：第2帧(最上层) > 第1帧 > 第0帧 > 第3帧(背景)")

    # 执行主处理（开启调试模式）
    final_image = main(images, bboxes_xyxy, keypoints_list, debug=True)

    if final_image is not None:
        print("处理完成！显示结果...")
        cv2.imshow('Enhanced GrabCut Motion Synthesis Result', final_image)
        cv2.imwrite('enhanced_final_result.jpg', final_image)
        cv2.waitKey(0)
        cv2.destroyAllWindows()
    else:
        print("处理失败！")
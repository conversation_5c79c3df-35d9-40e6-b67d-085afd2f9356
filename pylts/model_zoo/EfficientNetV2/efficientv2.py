# -*-coding:utf-8-*-
"""
Google AutoML 是 EfficientNetV2 模型(tensorflow. 2021.04)的官方实现: https://github.com/google/automl/tree/master/efficientnetv2
Creates a EfficientNetV2 Model as defined in:
Mingxing Tan, Quoc V. Le. (2021). EfficientNetV2: Smaller Models and Faster Training arXiv preprint arXiv:2104.00298.
import from https://github.com/d-li14/mobilenetv2.pytorch
与timm库权重兼容的、通用的EfficientNetV2  PyTorch实现 @Google gemini 2.5 pro preview 0605

核心改动：
1.  模型配置 外部化到MODEL_CONFIGS和VARIANT_DEFAULTS字典中。
2.  EfficientNetV2类被泛化，通过参数接收网络配置。
3.
"""
import torch
import torch.nn as nn
import math
from typing import List, Dict, Any

# 确保能从torch.hub加载权重
try:
    from torch.hub import load_state_dict_from_url
except ImportError:
    from torch.utils.model_zoo import load_url as load_state_dict_from_url

__all__ = ['effnetv2_s', 'effnetv2_m', 'effnetv2_l', 'effnetv2_xl', 'EfficientNetV2']

# 1. 模型配置外部化
MODEL_CONFIGS = {
    's': [
        [1, 24, 2, 1, False, 0], [4, 48, 4, 2, False, 0], [4, 64, 4, 2, False, 0],
        [4, 128, 6, 2, True, 1], [6, 160, 9, 1, True, 1], [6, 256, 15, 2, True, 1],
    ],
    'm': [
        [1, 24, 3, 1, False, 0], [4, 48, 5, 2, False, 0], [4, 80, 5, 2, False, 0],
        [4, 160, 7, 2, True, 1], [6, 176, 14, 1, True, 1], [6, 304, 18, 2, True, 1],
        [6, 512, 5, 1, True, 1],
    ],
    'l': [
        [1, 32, 4, 1, False, 0], [4, 64, 7, 2, False, 0], [4, 96, 7, 2, False, 0],
        [4, 192, 10, 2, True, 1], [6, 224, 19, 1, True, 1], [6, 384, 25, 2, True, 1],
        [6, 640, 7, 1, True, 1],
    ],
    'xl': [
        [1, 32, 4, 1, False, 0], [4, 64, 8, 2, False, 0], [4, 96, 8, 2, False, 0],
        [4, 192, 16, 2, True, 1], [6, 256, 24, 1, True, 1], [6, 512, 32, 2, True, 1],
        [6, 640, 8, 1, True, 1],
    ]
}

# 2. 存放模型变体特定的默认参数
VARIANT_DEFAULTS = {
    's': {
        'width_mult': 1.0, 'stem_channels': 24, 'head_channels': 1280, 'drop_rate': 0.2},
    'm': {
        'width_mult': 1.0, 'stem_channels': 24, 'head_channels': 1280, 'drop_rate': 0.3},
    'l': {
        'width_mult': 1.0, 'stem_channels': 32, 'head_channels': 1280, 'drop_rate': 0.3},
    'xl': {
        'width_mult': 1.0, 'stem_channels': 32, 'head_channels': 1280, 'drop_rate': 0.4}
}


# ---default 辅助函数和模块 ---
def _make_divisible(v, divisor=8, min_value=None):
    min_value = min_value or divisor
    new_v = max(min_value, int(v + divisor / 2) // divisor * divisor)
    if new_v < 0.9 * v: new_v += divisor
    return new_v


class SEBlock(nn.Module):
    def __init__(self, expanded_channels, block_input_channels, rd_ratio=0.25):
        super(SEBlock, self).__init__()
        rd_channels = _make_divisible(block_input_channels * rd_ratio)
        self.conv_reduce = nn.Conv2d(expanded_channels, rd_channels, 1, bias=True)
        self.act1 = nn.SiLU(inplace=True)
        self.conv_expand = nn.Conv2d(rd_channels, expanded_channels, 1, bias=True)
        self.gate = nn.Sigmoid()

    def forward(self, x):
        x_se = x.mean((2, 3), keepdim=True)
        return x * self.gate(self.conv_expand(self.act1(self.conv_reduce(x_se))))


class MBConvBlock(nn.Module):
    def __init__(self, in_chs, out_chs, stride, expand_ratio, use_se):
        super(MBConvBlock, self).__init__()
        mid_chs = in_chs * expand_ratio
        self.use_residual = (in_chs == out_chs and stride == 1)
        self.conv_pw, self.bn1 = nn.Conv2d(in_chs, mid_chs, 1, bias=False), nn.BatchNorm2d(mid_chs)
        self.act1 = nn.SiLU(inplace=True)
        self.conv_dw, self.bn2 = nn.Conv2d(mid_chs, mid_chs, 3, stride, 1, groups=mid_chs, bias=False), nn.BatchNorm2d(
            mid_chs)
        self.act2 = nn.SiLU(inplace=True)
        self.se = SEBlock(mid_chs, in_chs) if use_se else nn.Identity()
        self.conv_pwl, self.bn3 = nn.Conv2d(mid_chs, out_chs, 1, bias=False), nn.BatchNorm2d(out_chs)

    def forward(self, x):
        shortcut = x
        x = self.act1(self.bn1(self.conv_pw(x)))
        x = self.act2(self.bn2(self.conv_dw(x)))
        x = self.se(x)
        x = self.bn3(self.conv_pwl(x))
        if self.use_residual: x += shortcut
        return x


class FusedMBConvBlock(nn.Module):
    """
    FusedMBConvBlock 必须根据 expand_ratio 的值来决定其内部的模块命名和结构。
        1. 当 expand_ratio == 1 时 (Stage 0): 模块很简单，主卷积层应命名为 conv，并且没有后续的 conv_pwl 投影层。
        2. 当 expand_ratio > 1 时 (Stages 1, 2): 模块更复杂，主卷积层应命名为 conv_exp，并且必须有 conv_pwl 投影层来恢复通道数。
    """
    def __init__(self, in_chs, out_chs, stride, expand_ratio, use_se):
        super(FusedMBConvBlock, self).__init__()
        mid_chs = in_chs * expand_ratio
        self.use_residual, self.expand_ratio = (in_chs == out_chs and stride == 1), expand_ratio
        if self.expand_ratio > 1:
            self.conv_exp, self.bn1 = nn.Conv2d(in_chs, mid_chs, 3, stride, 1, bias=False), nn.BatchNorm2d(mid_chs)
            self.act1 = nn.SiLU(inplace=True)
            self.conv_pwl, self.bn2 = nn.Conv2d(mid_chs, out_chs, 1, bias=False), nn.BatchNorm2d(out_chs)
        else:
            self.conv, self.bn1 = nn.Conv2d(in_chs, out_chs, 3, stride, 1, bias=False), nn.BatchNorm2d(out_chs)
            self.act1 = nn.SiLU(inplace=True)
        self.se = SEBlock(mid_chs, in_chs) if use_se else nn.Identity()

    def forward(self, x):
        shortcut = x
        if self.expand_ratio > 1:
            x = self.act1(self.bn1(self.conv_exp(x)))
            x = self.se(x)
            x = self.bn2(self.conv_pwl(x))
        else:
            x = self.act1(self.bn1(self.conv(x)))
            x = self.se(x)
        if self.use_residual: x += shortcut
        return x


# 3. 通用化的EfficientNetV2模型类
class EfficientNetV2(nn.Module):
    def __init__(self, model_cfg: List, stem_channels: int, head_channels: int,
                 num_classes: int = 1000, width_mult: float = 1.0, drop_rate: float = 0.2):
        super().__init__()

        # Stem
        self.conv_stem, self.bn1 = nn.Conv2d(3, stem_channels, 3, 2, 1, bias=False), nn.BatchNorm2d(stem_channels)
        self.act1 = nn.SiLU(inplace=True)

        # Blocks
        self.blocks = nn.Sequential()
        in_channels = stem_channels
        # block_type: 0 for FusedMBConv, 1 for MBConv
        for i, (er, oc, nb, s, se, bt) in enumerate(model_cfg):
            out_channels = _make_divisible(oc * width_mult)
            stage = nn.Sequential()
            for j in range(nb):
                stride = s if j == 0 else 1
                block_class = FusedMBConvBlock if bt == 0 else MBConvBlock
                stage.add_module(str(j), block_class(in_channels, out_channels, stride, er, se))
                in_channels = out_channels
            self.blocks.add_module(str(i), stage)

        # Head
        self.conv_head, self.bn2 = nn.Conv2d(in_channels, head_channels, 1, bias=False), nn.BatchNorm2d(head_channels)
        self.act2 = nn.SiLU(inplace=True)
        self.global_pool, self.flatten = nn.AdaptiveAvgPool2d(1), nn.Flatten()
        self.dropout = nn.Dropout(p=drop_rate, inplace=True)
        self.classifier = nn.Linear(head_channels, num_classes)

        self._init_weights()

    def _init_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.ones_(m.weight); nn.init.zeros_(m.bias)
            elif isinstance(m, nn.Linear):
                nn.init.trunc_normal_(m.weight, std=.02)
                if m.bias is not None: nn.init.zeros_(m.bias)

    def forward(self, x):
        x = self.act1(self.bn1(self.conv_stem(x)))
        x = self.blocks(x)
        x = self.act2(self.bn2(self.conv_head(x)))
        x = self.global_pool(x)
        x = self.flatten(x)
        x = self.dropout(x)
        return self.classifier(x)


# 4. 创建模型的工厂函数
def _create_effnetv2(variant: str, **kwargs: Any) -> EfficientNetV2:
    """通用模型创建函数"""
    if variant not in MODEL_CONFIGS:
        raise ValueError(f"Unsupported variant: {variant}, available options: {list(MODEL_CONFIGS.keys())}")

    # 合并默认参数和用户自定义参数
    model_kwargs = VARIANT_DEFAULTS[variant].copy()
    model_kwargs.update(kwargs)

    # 提取模型配置和权重URL
    cfg = MODEL_CONFIGS[variant]

    # 创建模型实例
    model = EfficientNetV2(model_cfg=cfg, **model_kwargs)

    return model


# 5. 定义对外公开的接口
def effnetv2_s(**kwargs: Any) -> EfficientNetV2:
    return _create_effnetv2('s', **kwargs)


def effnetv2_m(**kwargs: Any) -> EfficientNetV2:
    return _create_effnetv2('m', **kwargs)


def effnetv2_l(**kwargs: Any) -> EfficientNetV2:
    return _create_effnetv2('l', **kwargs)


def effnetv2_xl(**kwargs: Any) -> EfficientNetV2:
    return _create_effnetv2('xl', **kwargs)


# 6. 使用示例
if __name__ == "__main__":
    # 加载L模型的权重
    model = effnetv2_s(num_classes=1000)
    model.eval()

    # 加载预训练权重
    save_path = '../pretrained_file/tf_efficientnetv2_s_in21k.pth'
    try:
        timm_weights = torch.load(save_path, map_location='cpu')
        print(f"成功在路径 {save_path} 找到并加载权重文件。")
        # 移除分类头权重，因为尺寸通常不匹配
        timm_weights.pop('classifier.weight', None)
        timm_weights.pop('classifier.bias', None)
    except FileNotFoundError:
        print(f"错误: 权重文件未找到: {save_path}")
        timm_weights = None

    # 加载权重
    load_weight_dict = {k: v for k, v in timm_weights.items() if model.state_dict()[k].numel() == v.numel()}
    missing_keys, unexpected_keys = model.load_state_dict(load_weight_dict, strict=False)

    print(f"Missing keys (should only be classifier): {missing_keys}")
    print(f"Unexpected keys (should be empty): {unexpected_keys}")
    if not unexpected_keys and all('classifier' in k for k in missing_keys):
        print("\033[92mSuccessfully loaded pretrained weights (except classifier).\033[0m")
    else:
        print("\033[91mWarning: Weight loading might have issues. Check lists above.\033[0m")


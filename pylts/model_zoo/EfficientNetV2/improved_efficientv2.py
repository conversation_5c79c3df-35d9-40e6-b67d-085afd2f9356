# -*-coding:utf-8-*-
"""
参考 免训练的动态聚焦视觉搜索方法 DyFo(https://github.com/PKU-ICST-MIPL/DyFo_CVPR2025),
    对efficientV2模型进行的改进 @claude4：
1. 改进的SE注意力机制（通道+空间）
2. 轻量级自注意力模块
3. 动态卷积
4. 渐进式训练策略
5. 更好的正则化和优化技术

"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from typing import List, Tuple, Optional
from torchvision import transforms

__all__ = ['ImprovedEfficientNetV2', 'improved_effnetv2_s', 'improved_effnetv2_m',
           'improved_effnetv2_l', 'improved_effnetv2_xl']

# 模型配置
MODEL_CONFIGS = {
    's': [  # EfficientNetV2-S
        # [expand_ratio, channels, num_blocks, stride, use_se, use_attention]
        [1, 24, 2, 1, False, False],
        [4, 48, 4, 2, False, False],
        [4, 64, 4, 2, False, True],  # 在中间层添加注意力
        [4, 128, 6, 2, True, True],
        [6, 160, 9, 1, True, True],
        [6, 256, 15, 2, True, True],
    ],
    'm': [  # EfficientNetV2-M
        [1, 24, 3, 1, False, False],
        [4, 48, 5, 2, False, False],
        [4, 80, 5, 2, False, True],
        [4, 160, 7, 2, True, True],
        [6, 176, 14, 1, True, True],
        [6, 304, 18, 2, True, True],
        [6, 512, 5, 1, True, True],
    ],
    'l': [  # EfficientNetV2-L
        [1, 32, 4, 1, False, False],
        [4, 64, 7, 2, False, False],
        [4, 96, 7, 2, False, True],
        [4, 192, 10, 2, True, True],
        [6, 224, 19, 1, True, True],
        [6, 384, 25, 2, True, True],
        [6, 640, 7, 1, True, True],
    ],
    'xl': [  # EfficientNetV2-XL
        [1, 32, 4, 1, False, False],
        [4, 64, 8, 2, False, False],
        [4, 96, 8, 2, False, True],
        [4, 192, 16, 2, True, True],
        [6, 256, 24, 1, True, True],
        [6, 512, 32, 2, True, True],
        [6, 640, 8, 1, True, True],
    ]
}


def make_divisible(v: int, divisor: int = 8, min_value: int = None) -> int:
    """确保通道数能被divisor整除"""
    if min_value is None:
        min_value = divisor
    new_v = max(min_value, int(v + divisor / 2) // divisor * divisor)
    if new_v < 0.9 * v:
        new_v += divisor
    return new_v


class ImprovedSEBlock(nn.Module):
    """改进的SE模块，支持空间和通道双重注意力"""

    def __init__(self, channels: int, reduction: int = 16):
        super().__init__()
        # 通道注意力
        self.channel_attention = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(channels, channels // reduction, 1),
            nn.SiLU(),
            nn.Conv2d(channels // reduction, channels, 1),
            nn.Sigmoid()
        )

        # 空间注意力
        self.spatial_attention = nn.Sequential(
            nn.Conv2d(2, 1, kernel_size=7, padding=3),
            nn.Sigmoid()
        )

    def forward(self, x):
        # 通道注意力
        ca = self.channel_attention(x)
        x = x * ca

        # 空间注意力
        avg_out = torch.mean(x, dim=1, keepdim=True)
        max_out, _ = torch.max(x, dim=1, keepdim=True)
        sa = self.spatial_attention(torch.cat([avg_out, max_out], dim=1))
        x = x * sa

        return x


class LightweightSelfAttention(nn.Module):
    """轻量级自注意力模块"""

    def __init__(self, channels: int, key_channels: Optional[int] = None):
        super().__init__()
        # 确保 key_channels 是 8 的倍数，这通常能提高计算效率
        key_channels = key_channels or make_divisible(channels // 8)

        self.key_channels = key_channels
        self.scale = self.key_channels ** -0.5

        self.f_key = nn.Conv2d(channels, key_channels, 1)
        self.f_query = nn.Conv2d(channels, key_channels, 1)
        self.f_value = nn.Conv2d(channels, channels, 1)
        self.gamma = nn.Parameter(torch.zeros(1))

    def forward(self, x):
        batch_size, channels, height, width = x.size()

        # 生成key, query, value
        key = self.f_key(x).view(batch_size, self.key_channels, -1)
        query = self.f_query(x).view(batch_size, self.key_channels, -1)
        value = self.f_value(x).view(batch_size, channels, -1)

        # 计算注意力权重

        attention = torch.bmm(query.transpose(1, 2), key) * self.scale       # 修正后：添加缩放因子
        attention = F.softmax(attention, dim=-1)

        # 应用注意力
        out = torch.bmm(value, attention.transpose(1, 2))
        out = out.view(batch_size, channels, height, width)

        # 注意：即时 gamma=0，如果 out 是 nan，`0 * nan` 结果还是 nan
        return self.gamma * out + x



class DynamicConv2d(nn.Module):
    """动态卷积模块，根据输入自适应调整卷积核"""

    def __init__(self, in_channels: int, out_channels: int, kernel_size: int,
                 stride: int = 1, padding: int = 0, n_kernels: int = 4, groups: int = 1):
        super().__init__()
        self.n_kernels = n_kernels
        self.out_channels = out_channels

        # 多个卷积核
        self.convs = nn.ModuleList([
            nn.Conv2d(in_channels, out_channels, kernel_size, stride, padding,
                      groups=groups, bias=False)
            for _ in range(n_kernels)
        ])

        # 注意力权重生成
        self.attention = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(in_channels, n_kernels, 1),
            nn.Softmax(dim=1)
        )

    def forward(self, x):
        # 计算注意力权重
        weights = self.attention(x)  # [B, n_kernels, 1, 1]

        # 应用动态权重
        outputs = []
        for i, conv in enumerate(self.convs):
            outputs.append(conv(x) * weights[:, i:i + 1, :, :])

        return sum(outputs)


class ConvBNAct(nn.Module):
    """与原版不同：use_dynamic
    动态卷积+BN+激活函数的组合"""

    def __init__(self, in_channels: int, out_channels: int, kernel_size: int = 1,
                 stride: int = 1, groups: int = 1, act: bool = True, use_dynamic: bool = False):
        super().__init__()
        padding = kernel_size // 2
        # 使用动态卷积
        if use_dynamic and kernel_size > 1:
            self.conv = DynamicConv2d(in_channels, out_channels, kernel_size,
                                      stride, padding, groups=groups)
        else:
            self.conv = nn.Conv2d(in_channels, out_channels, kernel_size, stride,
                                  padding, groups=groups, bias=False)

        self.bn = nn.BatchNorm2d(out_channels)
        self.act = nn.SiLU() if act else nn.Identity()

    def forward(self, x):
        return self.act(self.bn(self.conv(x)))





class ImprovedMBConvBlock(nn.Module):
    """改进的Mobile Inverted Residual Block"""

    def __init__(self, in_channels: int, out_channels: int, kernel_size: int = 3,
                 stride: int = 1, expand_ratio: int = 1, use_se: bool = False,
                 use_attention: bool = False, drop_path_rate: float = 0.0):
        super().__init__()
        self.use_residual = stride == 1 and in_channels == out_channels
        self.drop_path_rate = drop_path_rate
        hidden_channels = in_channels * expand_ratio

        layers = []

        # Fused MBConv vs Regular MBConv
        if expand_ratio == 1:
            # Fused: 3x3 conv + SE + attention + 1x1 conv
            layers.extend([
                ConvBNAct(in_channels, hidden_channels, kernel_size, stride,
                          use_dynamic=True),
                ImprovedSEBlock(hidden_channels) if use_se else nn.Identity(),
                LightweightSelfAttention(hidden_channels) if use_attention else nn.Identity(),
                ConvBNAct(hidden_channels, out_channels, 1, act=False)
            ])
        else:
            # Regular: 1x1 expand + 3x3 depthwise + SE + attention + 1x1 project
            layers.extend([
                ConvBNAct(in_channels, hidden_channels, 1),
                ConvBNAct(hidden_channels, hidden_channels, kernel_size, stride,
                          groups=hidden_channels, use_dynamic=True),
                ImprovedSEBlock(hidden_channels) if use_se else nn.Identity(),
                LightweightSelfAttention(hidden_channels) if use_attention else nn.Identity(),
                ConvBNAct(hidden_channels, out_channels, 1, act=False)
            ])

        self.block = nn.Sequential(*layers)

    @staticmethod
    def drop_path(x, drop_prob: float = 0., training: bool = False):
        """
        Drop paths (Stochastic Depth) per sample.
        From timm library.
        """
        if drop_prob == 0. or not training:
            return x
        keep_prob = 1 - drop_prob
        shape = (x.shape[0],) + (1,) * (x.ndim - 1)  # (B, 1, 1, 1)
        random_tensor = keep_prob + torch.rand(shape, dtype=x.dtype, device=x.device)
        random_tensor.floor_()  # binarize
        output = x.div(keep_prob) * random_tensor
        return output

    def forward(self, x):
        result = self.block(x)

        # 使用标准的 DropPath正则
        if self.use_residual:
            return x + self.drop_path(result, self.drop_path_rate, self.training)
        else:
            return result



class ImprovedEfficientNetV2(nn.Module):
    """改进的EfficientNetV2主模型"""

    def __init__(self, block_configs: List[List], num_classes: int = 1000,
                 width_mult: float = 1.0, drop_rate: float = 0.2,
                 drop_path_rate: float = 0.2):
        super().__init__()

        # Stem层
        stem_channels = make_divisible(24 * width_mult)
        self.stem = ConvBNAct(3, stem_channels, 3, 2, use_dynamic=True)

        # 构建所有MBConv块
        layers = []
        in_channels = stem_channels
        total_blocks = sum([num_blocks for _, _, num_blocks, _, _, _ in block_configs])
        block_idx = 0

        for expand_ratio, out_channels, num_blocks, stride, use_se, use_attention in block_configs:
            out_channels = make_divisible(out_channels * width_mult)

            for i in range(num_blocks):
                # 计算当前块的drop_path_rate
                current_drop_path_rate = drop_path_rate * block_idx / total_blocks

                layers.append(ImprovedMBConvBlock(
                    in_channels, out_channels,
                    stride=stride if i == 0 else 1,
                    expand_ratio=expand_ratio,
                    use_se=use_se,
                    use_attention=use_attention,
                    drop_path_rate=current_drop_path_rate
                ))
                in_channels = out_channels
                block_idx += 1

        self.blocks = nn.Sequential(*layers)

        # Head层
        head_channels = make_divisible(1792 * width_mult) if width_mult > 1.0 else 1792
        self.head = nn.Sequential(
            ConvBNAct(in_channels, head_channels, 1),
            nn.AdaptiveAvgPool2d(1),
            nn.Dropout(drop_rate),
            nn.Flatten(),
            nn.Linear(head_channels, num_classes)
        )

        # 初始化权重
        self._init_weights()

    def forward(self, x):
        x = self.stem(x)
        x = self.blocks(x)
        return self.head(x)

    def _init_weights(self):
        """改进的权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.ones_(m.weight)
                nn.init.zeros_(m.bias)
            elif isinstance(m, nn.Linear):
                nn.init.trunc_normal_(m.weight, std=0.02)
                nn.init.zeros_(m.bias)


def _create_improved_efficientnetv2(variant: str, **kwargs):
    """创建改进的EfficientNetV2模型的通用函数"""
    if variant not in MODEL_CONFIGS:
        raise ValueError(f"Unsupported variant: {variant}")
    return ImprovedEfficientNetV2(MODEL_CONFIGS[variant], **kwargs)


def improved_effnetv2_s(**kwargs):
    """构建改进的EfficientNetV2-S模型"""
    return _create_improved_efficientnetv2('s', **kwargs)


def improved_effnetv2_m(**kwargs):
    """构建改进的EfficientNetV2-M模型"""
    return _create_improved_efficientnetv2('m', **kwargs)


def improved_effnetv2_l(**kwargs):
    """构建改进的EfficientNetV2-L模型"""
    return _create_improved_efficientnetv2('l', **kwargs)


def improved_effnetv2_xl(**kwargs):
    """构建改进的EfficientNetV2-XL模型"""
    return _create_improved_efficientnetv2('xl', **kwargs)


# 以下是搭配使用的训练策略示例：

class ProgressiveTraining:
    """渐进式训练策略"""

    def __init__(self, model, initial_size: int = 224, target_size: int = 384, stages: int = 4):
        self.model = model
        self.initial_size = initial_size
        self.target_size = target_size
        self.stages = stages
        self.current_stage = 0

    def get_current_size(self, epoch: int, total_epochs: int) -> int:
        """根据训练进度调整输入尺寸"""
        stage_epochs = total_epochs // self.stages
        stage = min(epoch // stage_epochs, self.stages - 1)

        size_step = (self.target_size - self.initial_size) // (self.stages - 1)
        current_size = self.initial_size + stage * size_step

        return current_size

    def get_transform(self, size: int):
        """获取对应尺寸的数据变换"""
        return transforms.Compose([
            transforms.Resize((size, size)),
            transforms.RandomHorizontalFlip(),
            transforms.RandAugment(num_ops=2, magnitude=9),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406],
                                 std=[0.229, 0.224, 0.225])
        ])


# 训练配置类
class TrainingConfig:
    """训练配置"""

    def __init__(self):
        # 优化器配置
        self.optimizer = 'AdamW'
        self.learning_rate = 1e-3
        self.weight_decay = 0.05
        self.momentum = 0.9

        # 学习率调度
        self.scheduler = 'cosine'
        self.warmup_epochs = 5
        self.min_lr = 1e-6

        # 正则化
        self.drop_rate = 0.2
        self.drop_path_rate = 0.2
        self.mixup_alpha = 0.2
        self.cutmix_alpha = 1.0
        self.label_smoothing = 0.1

        # 渐进式训练
        self.progressive_training = True
        self.initial_size = 224
        self.target_size = 384
        self.training_stages = 4

    def get_optimizer(self, model):
        """获取优化器"""
        if self.optimizer == 'AdamW':
            return torch.optim.AdamW(model.parameters(),
                                     lr=self.learning_rate,
                                     weight_decay=self.weight_decay)
        elif self.optimizer == 'SGD':
            return torch.optim.SGD(model.parameters(),
                                   lr=self.learning_rate,
                                   momentum=self.momentum,
                                   weight_decay=self.weight_decay)
        else:
            raise ValueError(f"Unsupported optimizer: {self.optimizer}")


# 使用示例
if __name__ == "__main__":
    # 创建改进的模型
    model = improved_effnetv2_l(num_classes=4)

    # 测试前向传播
    x = torch.randn(2, 3, 224, 224)
    with torch.no_grad():
        output = model(x)

    print(f"输入形状: {x.shape}")
    print(f"输出形状: {output.shape}")
    print(f"参数量: {sum(p.numel() for p in model.parameters()):,}")   # s: 76,957,532     l: 458,585,399  xl: 458,585,399

    example_train = False
    if example_train:
        # 渐进式训练示例
        progressive_trainer = ProgressiveTraining(model)
        current_size = progressive_trainer.get_current_size(epoch=10, total_epochs=100)
        print(f"第10轮的输入尺寸: {current_size}")

        # 训练配置
        config = TrainingConfig()
        optimizer = config.get_optimizer(model)
        print(f"优化器: {optimizer}")

        # In your training loop
        # 在训练复杂模型时，引入**梯度裁剪（Gradient Clipping）**是一个很好的实践，可以防止梯度爆炸，进一步增强训练稳定性。
        # loss.backward()
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)  # 梯度裁剪
        optimizer.step()
        # ...
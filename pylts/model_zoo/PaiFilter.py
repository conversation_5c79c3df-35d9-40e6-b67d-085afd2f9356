"""
created by @Moss 20241225
Ref https://github.com/aikunyi/FilterNet
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import argparse
# from layers.RevIN import RevIN


class RevIN(nn.Module):
    def __init__(self, num_features: int, eps=1e-5, affine=True, subtract_last=False):
        """
        :param num_features: the number of features or channels
        :param eps: a value added for numerical stability
        :param affine: if True, RevIN has learnable affine parameters(可学习的仿射参数？)
        """
        super(RevIN, self).__init__()
        self.num_features = num_features
        self.eps = eps
        self.affine = affine
        self.subtract_last = subtract_last
        if self.affine:
            self._init_params()

    def forward(self, x, mode: str):
        if mode == 'norm':
            self._get_statistics(x)
            x = self._normalize(x)
        elif mode == 'denorm':
            x = self._denormalize(x)
        else: raise NotImplementedError
        return x

    def _init_params(self):
        # initialize RevIN params: (C,)
        self.affine_weight = nn.Parameter(torch.ones(self.num_features))
        self.affine_bias = nn.Parameter(torch.zeros(self.num_features))

    def _get_statistics(self, x):
        dim2reduce = tuple(range(1, x.ndim-1))
        if self.subtract_last:
            self.last = x[:,-1,:].unsqueeze(1)
        else:
            self.mean = torch.mean(x, dim=dim2reduce, keepdim=True).detach()
        self.stdev = torch.sqrt(torch.var(x, dim=dim2reduce, keepdim=True, unbiased=False) + self.eps).detach()

    def _normalize(self, x):
        if self.subtract_last:
            x = x - self.last
        else:
            x = x - self.mean
        x = x / self.stdev
        if self.affine:
            x = x * self.affine_weight
            x = x + self.affine_bias
        return x

    def _denormalize(self, x):
        if self.affine:
            x = x - self.affine_bias
            x = x / (self.affine_weight + self.eps*self.eps)
        x = x * self.stdev
        if self.subtract_last:
            x = x + self.last
        else:
            x = x + self.mean
        return x


class Model(nn.Module):

    def __init__(self, configs):
        super(Model, self).__init__()
        self.seq_len = configs.seq_len
        self.pred_len = configs.pred_len
        self.scale = 0.02
        self.revin_layer = RevIN(configs.enc_in, affine=True, subtract_last=False)

        self.embed_size = self.seq_len
        self.hidden_size = configs.hidden_size
        
        self.w = nn.Parameter(self.scale * torch.randn(1, self.embed_size))         # Parameter将一个不可训练的 Tensor 转换成可以训练的参数

        self.fc = nn.Sequential(
            nn.Linear(self.embed_size, self.hidden_size),
            nn.LeakyReLU(),
            nn.Linear(self.hidden_size, self.pred_len)
        )

        self.output_layer = nn.Linear(75*34, 3)

    def circular_convolution(self, x, w):
        x = torch.fft.rfft(x, dim=2, norm='ortho')
        w = torch.fft.rfft(w, dim=1, norm='ortho')
        y = x * w
        out = torch.fft.irfft(y, n=self.embed_size, dim=2, norm="ortho")
        return out

    def forward(self, x, x_mark_enc, x_dec, x_mark_dec, mask=None):
        z = x
        z = self.revin_layer(z, mode='norm')
        x = z

        x = x.permute(0, 2, 1)

        x = self.circular_convolution(x, self.w.to(x.device))  # B, N, D

        x = self.fc(x)
        x = x.permute(0, 2, 1)

        z = x
        z = self.revin_layer(z, mode='denorm')
        x = z

        # 加一个flatten 和
        flattened_input = x.flatten(1)
        output = self.output_layer(flattened_input)
        output = F.softmax(output, dim=1)

        return output


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='TimesNet')

    # forecasting task
    parser.add_argument('--seq_len', type=int, default=75, help='input sequence length')
    parser.add_argument('--pred_len', type=int, default=75, help='prediction sequence length, 分类模型？')

    # model define
    parser.add_argument('--enc_in', type=int, default=34, help='encoder input size | the number of features or channels')

    # FilterNet
    # parser.add_argument('--embed_size', default=128, type=int)        # 分类模型def: self.embed_size = self.seq_len
    # parser.add_argument('--dropout', type=float, default=0, help='dropout')
    parser.add_argument('--hidden_size', default=128, type=int)

    configs = parser.parse_args()

    PaiFilter = Model(configs).float()
    print(PaiFilter)

    # TODO 参考 /media/pyl/WD_Blue_1T/All_proj/TimeConv_classify/FilterNet-main/data_provider/data_loader.py
    # from sklearn.preprocessing import StandardScaler
    # scaler = StandardScaler()        # 预处理
    # scaler.fit(train_data.values)

    batch_x = torch.rand((1, 75, 34))
    outputs = PaiFilter(batch_x, None, None, None)
    print(outputs)
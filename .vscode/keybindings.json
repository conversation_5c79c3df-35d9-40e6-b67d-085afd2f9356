[{"key": "ctrl+shift+f5", "command": "workbench.action.debug.restart"}, {"key": "ctrl+f5", "command": "workbench.action.debug.run"}, {"key": "alt+f5", "command": "workbench.action.debug.continue"}, {"key": "alt+f10", "command": "workbench.action.debug.stepOver"}, {"key": "alt+f11", "command": "workbench.action.debug.stepInto"}, {"key": "alt+shift+f11", "command": "workbench.action.debug.stepOut"}, {"key": "ctrl+shift+d", "command": "workbench.view.debug", "when": "!debuggersAvailable"}, {"key": "ctrl+shift+y", "command": "workbench.debug.action.toggleRepl"}]
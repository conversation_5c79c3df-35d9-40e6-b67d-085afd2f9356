{"python.defaultInterpreterPath": "python", "python.terminal.activateEnvironment": true, "python.analysis.typeCheckingMode": "basic", "python.linting.enabled": true, "python.linting.pylintEnabled": true, "python.formatting.provider": "black", "python.testing.pytestEnabled": true, "python.testing.unittestEnabled": false, "python.testing.nosetestsEnabled": false, "editor.rulers": [88, 120], "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": "explicit"}, "debug.inlineValues": true, "debug.showVariablesHover": true, "debug.console.fontSize": 14, "debug.console.fontFamily": "JetBrains Mono, Consolas, monospace", "debug.toolBarLocation": "docked", "debug.hideLauncherWhileDebugging": false, "debug.showInStatusBar": "always", "debug.focusWindowOnBreak": true, "debug.console.acceptSuggestionOnEnter": "on", "workbench.panel.defaultLocation": "bottom", "workbench.panel.opensMaximized": "never", "debug.allowBreakpointsEverywhere": true, "files.exclude": {"**/__pycache__": true, "**/*.pyc": true, "**/logs": true}}
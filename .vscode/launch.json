{"version": "0.2.0", "configurations": [{"name": "Launch Chrome", "request": "launch", "type": "chrome", "url": "http://localhost:8080", "webRoot": "${workspaceFolder}"}, {"name": "Python: 当前文件", "type": "python", "request": "launch", "program": "${file}", "console": "integratedTerminal", "justMyCode": true, "cwd": "${workspaceFolder}"}, {"name": "Python: 主程序 (main.py)", "type": "python", "request": "launch", "program": "${workspaceFolder}/main.py", "console": "integratedTerminal", "justMyCode": true, "cwd": "${workspaceFolder}", "args": []}, {"name": "Python: 运行脚本 (run.py)", "type": "python", "request": "launch", "program": "${workspaceFolder}/run.py", "console": "integratedTerminal", "justMyCode": true, "cwd": "${workspaceFolder}", "args": []}, {"name": "Python: 训练模式", "type": "python", "request": "launch", "program": "${workspaceFolder}/tools/train.py", "console": "integratedTerminal", "justMyCode": true, "cwd": "${workspaceFolder}", "args": ["--config", "Configs/default.yaml"]}, {"name": "Python: 推理模式", "type": "python", "request": "launch", "program": "${workspaceFolder}/tools/infer.py", "console": "integratedTerminal", "justMyCode": true, "cwd": "${workspaceFolder}", "args": []}, {"name": "Python: 附加到进程", "type": "python", "request": "attach", "port": 5678, "host": "localhost"}]}
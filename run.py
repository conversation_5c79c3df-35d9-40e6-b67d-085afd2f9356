# -*-coding:utf-8-*-
import yaml
import torch
import random
import numpy as np

print(torch.__version__)


def setup_seed(fix_seed) -> None:
    """torch　在GPU或CPU上固定每次训练的结果"""
    random.seed(fix_seed)
    np.random.seed(fix_seed)
    torch.manual_seed(fix_seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(fix_seed)  # 设置所有GPU的随机数种子
        # torch.backends.cudnn.deterministic = True         # 严格复现，会影响训练速度


if __name__ == '__main__':
    setup_seed(2023)

    # Set logger

    # Params
    with open('Configs/default.yaml', 'r', encoding='utf-8') as f:
        params = yaml.load(f.read(), Loader=yaml.FullLoader)

    print(params)

    # TODO 值得模仿的建造者模式
    '''
    optimizer = build_optimizer(model, cfg.optimizer)   # cfg.optimizer={'type': 'SGD', 'lr': 0.2, 'momentum': 0.9, 'weight_decay': 0.0003}
    '''

    # TODO 参考：
    class __Registry:  # 提供全局注册器功能：
        def __init__(self, name):
            self._name = name
            self._registry = {}

        def register_module(self, module=None, force=False):
            def _register_module(module):
                if not hasattr(module, '__init__'):
                    raise TypeError('module must be a callable or a class')
                module_name = module.__name__
                if not force and module_name in self._registry:
                    raise KeyError(f'{module_name} is already registered in {self._name}')
                self._registry[module_name] = module
                return module

            if module is not None:
                return _register_module(module)
            else:
                return _register_module

    #  DDP  能用于多机多卡的场景，基于多进程方式实现

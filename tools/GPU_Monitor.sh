#!/bin/bash

# 使用方法：
  # 设置需要监控的显卡, 间隔WaitTime秒 查看1次显存占用率,
  # 所有监控的显卡 都低于占用率阈值时，执行设置好的命令(command); 否则Sleep WaitTime秒继续监测；
  # 单命令/调用外部脚本 的简单写法：
        # command="echo Your train_sport.sh"      # 定义命令
        # eval "$command"                         #  配合 使用 eval 执行传入的命令
        # monitor_gpu "$GPU_IDS" "$THRESHOLD" "$WaitTime" "$command"    # 传参方式 "$command"
  # 复杂命令 写法: # command_func(){}


# 指定要监测的显卡 GPU ID 列表（用逗号分隔）
GPU_IDS="0"       # "0,1,2,3"
# 设定显存占用率临界值
THRESHOLD=5       # %
# 设置监测间隔时间(s)
WaitTime=3

# command="echo Your train_sport.sh"        # 简单方式
command_func() {
    echo 'Start'
    bash train_sport.sh
}



# 定义监测函数，接受多个参数："$GPU_IDS" "$THRESHOLD" "$WaitTime" "$要执行的命令"
monitor_gpu() {
    # 获取传递给函数的 GPU ID 列表参数
    GPU_IDS="$1"
    # 将 GPU ID 列表转换为以空格分隔的格式
    GPU_IDS_LIST=$(echo $GPU_IDS | tr ',' ' ')
    # 定义显存占用率临界值和监测间隔时间
    THRESHOLD="$2"  # %
    WaitTime="$3"

    # 获取传递给函数的要执行的命令
    command="$4"


    while true; do
        # 假设所有显卡都满足条件
        ALL_BELOW_THRESHOLD=1

        for GPU_ID in $GPU_IDS_LIST; do
            # 获取指定 GPU 的显存使用量和总显存大小
            VRAM_INFO=$(nvidia-smi --query-gpu=memory.used,memory.total --format=csv -i $GPU_ID)
            # 读取显存使用量和总显存大小（单位：MiB）
            read -r VRAM_USED_STR VRAM_TOTAL_STR <<< "$(grep -v '^$' <<< "$VRAM_INFO" | tail -n 1 | awk -F ', ' '{print $1,$2}')"

            # 提取数值部分（去掉单位）这里相当于提取nvidia-smi的格式化输出
            VRAM_USED=$(echo $VRAM_USED_STR | grep -o -E '[0-9]+')
            VRAM_TOTAL=$(echo $VRAM_TOTAL_STR | grep -o -E '[0-9]+')

            # 检查变量是否为空
            if [ -z "$VRAM_USED" ] || [ -z "$VRAM_TOTAL" ]; then
                echo "GPU ${GPU_ID}: Failed to extract VRAM information, setting to 0."
                VRAM_USED=0
                VRAM_TOTAL=1  # 防止除零错误
            fi

            # 避免除零错误
            if [ "$VRAM_TOTAL" -eq 0 ]; then
                echo "GPU ${GPU_ID} Total VRAM is 0, skip calculation."
                VRAM_PERCENTAGE=0
            else
                # 计算显存占用百分比（保留 2 位小数）
                VRAM_PERCENTAGE=$(awk -v used="$VRAM_USED" -v total="$VRAM_TOTAL" 'BEGIN {printf "%.2f", (used / total) * 100}')
            fi

            # 输出当前显存使用情况（便于调试）
            echo "GPU ${GPU_ID} 显存使用情况：${VRAM_USED_STR} / ${VRAM_TOTAL_STR}（Used-Rate：${VRAM_PERCENTAGE}%）"

            # 判断显存占用率是否大于设定的阈值（使用 awk 代替 bc 进行浮点比较）
            if [ $(awk -v perc="$VRAM_PERCENTAGE" -v th="$THRESHOLD" 'BEGIN {print (perc + 0 > th + 0)}') -eq 1 ]; then
                echo "GPU ${GPU_ID} Occupancy > Thres $THRESHOLD%，Continue Wait..."
                # 只要有一张显卡不满足条件，就将标志设置为 0
                ALL_BELOW_THRESHOLD=0
                break
            fi
        done

        # 如果所有显卡都满足条件，则执行传入的命令
        if [ $ALL_BELOW_THRESHOLD -eq 1 ]; then
            echo "All Set GPU Occupancy <= $THRESHOLD%，Start Run My Task as follow..."
            # eval "$command"       # 配合 使用 eval 执行传入的命令
            command_func            # 命令函数方式
            break
        fi

        # 设置监测间隔（秒）
        sleep ${WaitTime}
        echo "Sleep And Wait ${WaitTime} s."
    done
}

# 调用函数，传入 GPU ID 列表和要执行的命令
# monitor_gpu "$GPU_IDS" "$THRESHOLD" "$WaitTime" "$command"        # 方式1
monitor_gpu "$GPU_IDS" "$THRESHOLD" "$WaitTime" "command_func"
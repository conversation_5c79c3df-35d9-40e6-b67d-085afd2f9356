{"mcpServers": {"mcp-feedback-enhanced": {"command": "uvx", "args": ["mcp-feedback-enhanced@latest"], "timeout": 600, "autoApprove": ["interactive_feedback"]}, "Context 7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "env": {}}, "Sequential thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "env": {}}, "Filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem"], "env": {"ALLOWED_DIRECTORIES": "/media/pyl/WD_Blue_1T/All_proj,/media/pyl/WD_Blue_1T/conda-envs"}}, "Playwright": {"command": "npx", "args": ["-y", "@playwright/mcp@latest"], "env": {}}}}
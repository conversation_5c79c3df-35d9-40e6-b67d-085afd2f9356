('/media/pyl/WD_Blue_1T/All_proj/Task-Series-Factory/build/merge_docx/PYZ-00.pyz',
 [('__future__',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/__future__.py',
   'PYMODULE'),
  ('_aix_support',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/_bootsubprocess.py',
   'PYMODULE'),
  ('_compat_pickle',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/_compression.py',
   'PYMODULE'),
  ('_markupbase',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/_markupbase.py',
   'PYMODULE'),
  ('_py_abc',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/_py_abc.py',
   'PYMODULE'),
  ('_pydecimal',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/_pydecimal.py',
   'PYMODULE'),
  ('_strptime',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/_strptime.py',
   'PYMODULE'),
  ('_sysconfigdata__linux_x86_64-linux-gnu',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/_sysconfigdata__linux_x86_64-linux-gnu.py',
   'PYMODULE'),
  ('_threading_local',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/_threading_local.py',
   'PYMODULE'),
  ('argparse',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/argparse.py',
   'PYMODULE'),
  ('ast', '/home/<USER>/anaconda3/envs/torch/lib/python3.9/ast.py', 'PYMODULE'),
  ('asyncio',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/locks.py',
   'PYMODULE'),
  ('asyncio.log',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/log.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/subprocess.py',
   'PYMODULE'),
  ('asyncio.tasks',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/threads.py',
   'PYMODULE'),
  ('asyncio.transports',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/windows_utils.py',
   'PYMODULE'),
  ('base64',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/base64.py',
   'PYMODULE'),
  ('bdb', '/home/<USER>/anaconda3/envs/torch/lib/python3.9/bdb.py', 'PYMODULE'),
  ('bisect',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/bisect.py',
   'PYMODULE'),
  ('bs4',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/bs4/__init__.py',
   'PYMODULE'),
  ('bs4._deprecation',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/bs4/_deprecation.py',
   'PYMODULE'),
  ('bs4._typing',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/bs4/_typing.py',
   'PYMODULE'),
  ('bs4._warnings',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/bs4/_warnings.py',
   'PYMODULE'),
  ('bs4.builder',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/bs4/builder/__init__.py',
   'PYMODULE'),
  ('bs4.builder._html5lib',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/bs4/builder/_html5lib.py',
   'PYMODULE'),
  ('bs4.builder._htmlparser',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/bs4/builder/_htmlparser.py',
   'PYMODULE'),
  ('bs4.builder._lxml',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/bs4/builder/_lxml.py',
   'PYMODULE'),
  ('bs4.css',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/bs4/css.py',
   'PYMODULE'),
  ('bs4.dammit',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/bs4/dammit.py',
   'PYMODULE'),
  ('bs4.element',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/bs4/element.py',
   'PYMODULE'),
  ('bs4.exceptions',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/bs4/exceptions.py',
   'PYMODULE'),
  ('bs4.filter',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/bs4/filter.py',
   'PYMODULE'),
  ('bs4.formatter',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/bs4/formatter.py',
   'PYMODULE'),
  ('bz2', '/home/<USER>/anaconda3/envs/torch/lib/python3.9/bz2.py', 'PYMODULE'),
  ('calendar',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/calendar.py',
   'PYMODULE'),
  ('cgi', '/home/<USER>/anaconda3/envs/torch/lib/python3.9/cgi.py', 'PYMODULE'),
  ('charset_normalizer',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/charset_normalizer/__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/charset_normalizer/api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/charset_normalizer/cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/charset_normalizer/constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/charset_normalizer/legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/charset_normalizer/models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/charset_normalizer/utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/charset_normalizer/version.py',
   'PYMODULE'),
  ('cmd', '/home/<USER>/anaconda3/envs/torch/lib/python3.9/cmd.py', 'PYMODULE'),
  ('code', '/home/<USER>/anaconda3/envs/torch/lib/python3.9/code.py', 'PYMODULE'),
  ('codeop',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/codeop.py',
   'PYMODULE'),
  ('concurrent',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/concurrent/__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/concurrent/futures/__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/concurrent/futures/_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/concurrent/futures/process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/concurrent/futures/thread.py',
   'PYMODULE'),
  ('configparser',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/configparser.py',
   'PYMODULE'),
  ('contextlib',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/contextlib.py',
   'PYMODULE'),
  ('contextvars',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/contextvars.py',
   'PYMODULE'),
  ('copy', '/home/<USER>/anaconda3/envs/torch/lib/python3.9/copy.py', 'PYMODULE'),
  ('csv', '/home/<USER>/anaconda3/envs/torch/lib/python3.9/csv.py', 'PYMODULE'),
  ('ctypes',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/ctypes/__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/ctypes/_endian.py',
   'PYMODULE'),
  ('datetime',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/datetime.py',
   'PYMODULE'),
  ('decimal',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/decimal.py',
   'PYMODULE'),
  ('difflib',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/difflib.py',
   'PYMODULE'),
  ('dis', '/home/<USER>/anaconda3/envs/torch/lib/python3.9/dis.py', 'PYMODULE'),
  ('doctest',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/doctest.py',
   'PYMODULE'),
  ('docx',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/__init__.py',
   'PYMODULE'),
  ('docx.api',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/api.py',
   'PYMODULE'),
  ('docx.blkcntnr',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/blkcntnr.py',
   'PYMODULE'),
  ('docx.dml',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/dml/__init__.py',
   'PYMODULE'),
  ('docx.dml.color',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/dml/color.py',
   'PYMODULE'),
  ('docx.document',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/document.py',
   'PYMODULE'),
  ('docx.drawing',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/drawing/__init__.py',
   'PYMODULE'),
  ('docx.enum',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/enum/__init__.py',
   'PYMODULE'),
  ('docx.enum.base',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/enum/base.py',
   'PYMODULE'),
  ('docx.enum.dml',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/enum/dml.py',
   'PYMODULE'),
  ('docx.enum.section',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/enum/section.py',
   'PYMODULE'),
  ('docx.enum.shape',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/enum/shape.py',
   'PYMODULE'),
  ('docx.enum.style',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/enum/style.py',
   'PYMODULE'),
  ('docx.enum.table',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/enum/table.py',
   'PYMODULE'),
  ('docx.enum.text',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/enum/text.py',
   'PYMODULE'),
  ('docx.exceptions',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/exceptions.py',
   'PYMODULE'),
  ('docx.image',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/image/__init__.py',
   'PYMODULE'),
  ('docx.image.bmp',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/image/bmp.py',
   'PYMODULE'),
  ('docx.image.constants',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/image/constants.py',
   'PYMODULE'),
  ('docx.image.exceptions',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/image/exceptions.py',
   'PYMODULE'),
  ('docx.image.gif',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/image/gif.py',
   'PYMODULE'),
  ('docx.image.helpers',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/image/helpers.py',
   'PYMODULE'),
  ('docx.image.image',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/image/image.py',
   'PYMODULE'),
  ('docx.image.jpeg',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/image/jpeg.py',
   'PYMODULE'),
  ('docx.image.png',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/image/png.py',
   'PYMODULE'),
  ('docx.image.tiff',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/image/tiff.py',
   'PYMODULE'),
  ('docx.opc',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/opc/__init__.py',
   'PYMODULE'),
  ('docx.opc.constants',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/opc/constants.py',
   'PYMODULE'),
  ('docx.opc.coreprops',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/opc/coreprops.py',
   'PYMODULE'),
  ('docx.opc.exceptions',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/opc/exceptions.py',
   'PYMODULE'),
  ('docx.opc.oxml',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/opc/oxml.py',
   'PYMODULE'),
  ('docx.opc.package',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/opc/package.py',
   'PYMODULE'),
  ('docx.opc.packuri',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/opc/packuri.py',
   'PYMODULE'),
  ('docx.opc.part',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/opc/part.py',
   'PYMODULE'),
  ('docx.opc.parts',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/opc/parts/__init__.py',
   'PYMODULE'),
  ('docx.opc.parts.coreprops',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/opc/parts/coreprops.py',
   'PYMODULE'),
  ('docx.opc.phys_pkg',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/opc/phys_pkg.py',
   'PYMODULE'),
  ('docx.opc.pkgreader',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/opc/pkgreader.py',
   'PYMODULE'),
  ('docx.opc.pkgwriter',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/opc/pkgwriter.py',
   'PYMODULE'),
  ('docx.opc.rel',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/opc/rel.py',
   'PYMODULE'),
  ('docx.opc.shared',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/opc/shared.py',
   'PYMODULE'),
  ('docx.opc.spec',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/opc/spec.py',
   'PYMODULE'),
  ('docx.oxml',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/oxml/__init__.py',
   'PYMODULE'),
  ('docx.oxml.coreprops',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/oxml/coreprops.py',
   'PYMODULE'),
  ('docx.oxml.document',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/oxml/document.py',
   'PYMODULE'),
  ('docx.oxml.drawing',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/oxml/drawing.py',
   'PYMODULE'),
  ('docx.oxml.exceptions',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/oxml/exceptions.py',
   'PYMODULE'),
  ('docx.oxml.ns',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/oxml/ns.py',
   'PYMODULE'),
  ('docx.oxml.numbering',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/oxml/numbering.py',
   'PYMODULE'),
  ('docx.oxml.parser',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/oxml/parser.py',
   'PYMODULE'),
  ('docx.oxml.section',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/oxml/section.py',
   'PYMODULE'),
  ('docx.oxml.settings',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/oxml/settings.py',
   'PYMODULE'),
  ('docx.oxml.shape',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/oxml/shape.py',
   'PYMODULE'),
  ('docx.oxml.shared',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/oxml/shared.py',
   'PYMODULE'),
  ('docx.oxml.simpletypes',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/oxml/simpletypes.py',
   'PYMODULE'),
  ('docx.oxml.styles',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/oxml/styles.py',
   'PYMODULE'),
  ('docx.oxml.table',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/oxml/table.py',
   'PYMODULE'),
  ('docx.oxml.text',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/oxml/text/__init__.py',
   'PYMODULE'),
  ('docx.oxml.text.font',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/oxml/text/font.py',
   'PYMODULE'),
  ('docx.oxml.text.hyperlink',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/oxml/text/hyperlink.py',
   'PYMODULE'),
  ('docx.oxml.text.pagebreak',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/oxml/text/pagebreak.py',
   'PYMODULE'),
  ('docx.oxml.text.paragraph',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/oxml/text/paragraph.py',
   'PYMODULE'),
  ('docx.oxml.text.parfmt',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/oxml/text/parfmt.py',
   'PYMODULE'),
  ('docx.oxml.text.run',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/oxml/text/run.py',
   'PYMODULE'),
  ('docx.oxml.xmlchemy',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/oxml/xmlchemy.py',
   'PYMODULE'),
  ('docx.package',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/package.py',
   'PYMODULE'),
  ('docx.parts',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/parts/__init__.py',
   'PYMODULE'),
  ('docx.parts.document',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/parts/document.py',
   'PYMODULE'),
  ('docx.parts.hdrftr',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/parts/hdrftr.py',
   'PYMODULE'),
  ('docx.parts.image',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/parts/image.py',
   'PYMODULE'),
  ('docx.parts.numbering',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/parts/numbering.py',
   'PYMODULE'),
  ('docx.parts.settings',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/parts/settings.py',
   'PYMODULE'),
  ('docx.parts.story',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/parts/story.py',
   'PYMODULE'),
  ('docx.parts.styles',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/parts/styles.py',
   'PYMODULE'),
  ('docx.section',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/section.py',
   'PYMODULE'),
  ('docx.settings',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/settings.py',
   'PYMODULE'),
  ('docx.shape',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/shape.py',
   'PYMODULE'),
  ('docx.shared',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/shared.py',
   'PYMODULE'),
  ('docx.styles',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/styles/__init__.py',
   'PYMODULE'),
  ('docx.styles.latent',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/styles/latent.py',
   'PYMODULE'),
  ('docx.styles.style',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/styles/style.py',
   'PYMODULE'),
  ('docx.styles.styles',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/styles/styles.py',
   'PYMODULE'),
  ('docx.table',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/table.py',
   'PYMODULE'),
  ('docx.text',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/text/__init__.py',
   'PYMODULE'),
  ('docx.text.font',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/text/font.py',
   'PYMODULE'),
  ('docx.text.hyperlink',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/text/hyperlink.py',
   'PYMODULE'),
  ('docx.text.pagebreak',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/text/pagebreak.py',
   'PYMODULE'),
  ('docx.text.paragraph',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/text/paragraph.py',
   'PYMODULE'),
  ('docx.text.parfmt',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/text/parfmt.py',
   'PYMODULE'),
  ('docx.text.run',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/text/run.py',
   'PYMODULE'),
  ('docx.text.tabstops',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/text/tabstops.py',
   'PYMODULE'),
  ('docx.types',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/types.py',
   'PYMODULE'),
  ('email',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/email/__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/email/_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/email/_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/email/_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/email/_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/email/base64mime.py',
   'PYMODULE'),
  ('email.charset',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/email/charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/email/contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/email/encoders.py',
   'PYMODULE'),
  ('email.errors',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/email/errors.py',
   'PYMODULE'),
  ('email.feedparser',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/email/feedparser.py',
   'PYMODULE'),
  ('email.generator',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/email/generator.py',
   'PYMODULE'),
  ('email.header',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/email/header.py',
   'PYMODULE'),
  ('email.headerregistry',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/email/headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/email/iterators.py',
   'PYMODULE'),
  ('email.message',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/email/message.py',
   'PYMODULE'),
  ('email.parser',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/email/parser.py',
   'PYMODULE'),
  ('email.policy',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/email/policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/email/quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/email/utils.py',
   'PYMODULE'),
  ('fnmatch',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/fnmatch.py',
   'PYMODULE'),
  ('fractions',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/fractions.py',
   'PYMODULE'),
  ('ftplib',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/ftplib.py',
   'PYMODULE'),
  ('getopt',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/getopt.py',
   'PYMODULE'),
  ('getpass',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/getpass.py',
   'PYMODULE'),
  ('gettext',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/gettext.py',
   'PYMODULE'),
  ('glob', '/home/<USER>/anaconda3/envs/torch/lib/python3.9/glob.py', 'PYMODULE'),
  ('gzip', '/home/<USER>/anaconda3/envs/torch/lib/python3.9/gzip.py', 'PYMODULE'),
  ('hashlib',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/hashlib.py',
   'PYMODULE'),
  ('hmac', '/home/<USER>/anaconda3/envs/torch/lib/python3.9/hmac.py', 'PYMODULE'),
  ('html',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/html/__init__.py',
   'PYMODULE'),
  ('html.entities',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/html/entities.py',
   'PYMODULE'),
  ('html.parser',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/html/parser.py',
   'PYMODULE'),
  ('http',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/http/__init__.py',
   'PYMODULE'),
  ('http.client',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/http/client.py',
   'PYMODULE'),
  ('http.cookiejar',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/http/cookiejar.py',
   'PYMODULE'),
  ('http.server',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/http/server.py',
   'PYMODULE'),
  ('importlib',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/importlib/__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/importlib/_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/importlib/_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/importlib/abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/importlib/machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/importlib/metadata.py',
   'PYMODULE'),
  ('importlib.util',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/importlib/util.py',
   'PYMODULE'),
  ('inspect',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/inspect.py',
   'PYMODULE'),
  ('ipaddress',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/ipaddress.py',
   'PYMODULE'),
  ('json',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/json/__init__.py',
   'PYMODULE'),
  ('json.decoder',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/json/decoder.py',
   'PYMODULE'),
  ('json.encoder',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/json/encoder.py',
   'PYMODULE'),
  ('json.scanner',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/json/scanner.py',
   'PYMODULE'),
  ('logging',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/logging/__init__.py',
   'PYMODULE'),
  ('lxml',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/__init__.py',
   'PYMODULE'),
  ('lxml.ElementInclude',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/ElementInclude.py',
   'PYMODULE'),
  ('lxml.cssselect',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/cssselect.py',
   'PYMODULE'),
  ('lxml.doctestcompare',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/doctestcompare.py',
   'PYMODULE'),
  ('lxml.html',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/html/__init__.py',
   'PYMODULE'),
  ('lxml.html.ElementSoup',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/html/ElementSoup.py',
   'PYMODULE'),
  ('lxml.html._diffcommand',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/html/_diffcommand.py',
   'PYMODULE'),
  ('lxml.html._html5builder',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/html/_html5builder.py',
   'PYMODULE'),
  ('lxml.html._setmixin',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/html/_setmixin.py',
   'PYMODULE'),
  ('lxml.html.builder',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/html/builder.py',
   'PYMODULE'),
  ('lxml.html.clean',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/html/clean.py',
   'PYMODULE'),
  ('lxml.html.defs',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/html/defs.py',
   'PYMODULE'),
  ('lxml.html.formfill',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/html/formfill.py',
   'PYMODULE'),
  ('lxml.html.html5parser',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/html/html5parser.py',
   'PYMODULE'),
  ('lxml.html.soupparser',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/html/soupparser.py',
   'PYMODULE'),
  ('lxml.html.usedoctest',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/html/usedoctest.py',
   'PYMODULE'),
  ('lxml.includes',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/includes/__init__.py',
   'PYMODULE'),
  ('lxml.includes.extlibs',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/includes/extlibs/__init__.py',
   'PYMODULE'),
  ('lxml.includes.libexslt',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/includes/libexslt/__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxml',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/includes/libxml/__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxslt',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/includes/libxslt/__init__.py',
   'PYMODULE'),
  ('lxml.isoschematron',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/isoschematron/__init__.py',
   'PYMODULE'),
  ('lxml.pyclasslookup',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/pyclasslookup.py',
   'PYMODULE'),
  ('lxml.usedoctest',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/usedoctest.py',
   'PYMODULE'),
  ('lzma', '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lzma.py', 'PYMODULE'),
  ('mimetypes',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/mimetypes.py',
   'PYMODULE'),
  ('multiprocessing',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/multiprocessing/__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/multiprocessing/connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/multiprocessing/context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/multiprocessing/dummy/__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/multiprocessing/dummy/connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/multiprocessing/forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/multiprocessing/heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/multiprocessing/managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/multiprocessing/pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/multiprocessing/popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/multiprocessing/popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/multiprocessing/popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/multiprocessing/popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/multiprocessing/process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/multiprocessing/queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/multiprocessing/reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/multiprocessing/resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/multiprocessing/resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/multiprocessing/shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/multiprocessing/sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/multiprocessing/spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/multiprocessing/synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/multiprocessing/util.py',
   'PYMODULE'),
  ('netrc',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/netrc.py',
   'PYMODULE'),
  ('nturl2path',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/nturl2path.py',
   'PYMODULE'),
  ('numbers',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/numbers.py',
   'PYMODULE'),
  ('opcode',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/opcode.py',
   'PYMODULE'),
  ('optparse',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/optparse.py',
   'PYMODULE'),
  ('pathlib',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/pathlib.py',
   'PYMODULE'),
  ('pdb', '/home/<USER>/anaconda3/envs/torch/lib/python3.9/pdb.py', 'PYMODULE'),
  ('pickle',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/pickle.py',
   'PYMODULE'),
  ('pkgutil',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/pkgutil.py',
   'PYMODULE'),
  ('platform',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/platform.py',
   'PYMODULE'),
  ('pprint',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/pprint.py',
   'PYMODULE'),
  ('py_compile',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/py_compile.py',
   'PYMODULE'),
  ('pydoc',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/pydoc.py',
   'PYMODULE'),
  ('pydoc_data',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/pydoc_data/__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/pydoc_data/topics.py',
   'PYMODULE'),
  ('queue',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/queue.py',
   'PYMODULE'),
  ('quopri',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/quopri.py',
   'PYMODULE'),
  ('random',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/random.py',
   'PYMODULE'),
  ('runpy',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/runpy.py',
   'PYMODULE'),
  ('secrets',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/secrets.py',
   'PYMODULE'),
  ('selectors',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/selectors.py',
   'PYMODULE'),
  ('shlex',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/shlex.py',
   'PYMODULE'),
  ('shutil',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/shutil.py',
   'PYMODULE'),
  ('signal',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/signal.py',
   'PYMODULE'),
  ('socket',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/socket.py',
   'PYMODULE'),
  ('socketserver',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/socketserver.py',
   'PYMODULE'),
  ('soupsieve',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/soupsieve/__init__.py',
   'PYMODULE'),
  ('soupsieve.__meta__',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/soupsieve/__meta__.py',
   'PYMODULE'),
  ('soupsieve.css_match',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/soupsieve/css_match.py',
   'PYMODULE'),
  ('soupsieve.css_parser',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/soupsieve/css_parser.py',
   'PYMODULE'),
  ('soupsieve.css_types',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/soupsieve/css_types.py',
   'PYMODULE'),
  ('soupsieve.pretty',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/soupsieve/pretty.py',
   'PYMODULE'),
  ('soupsieve.util',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/soupsieve/util.py',
   'PYMODULE'),
  ('ssl', '/home/<USER>/anaconda3/envs/torch/lib/python3.9/ssl.py', 'PYMODULE'),
  ('statistics',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/statistics.py',
   'PYMODULE'),
  ('string',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/string.py',
   'PYMODULE'),
  ('stringprep',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/stringprep.py',
   'PYMODULE'),
  ('subprocess',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/subprocess.py',
   'PYMODULE'),
  ('sysconfig',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/sysconfig.py',
   'PYMODULE'),
  ('tarfile',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/tarfile.py',
   'PYMODULE'),
  ('tempfile',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/tempfile.py',
   'PYMODULE'),
  ('textwrap',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/textwrap.py',
   'PYMODULE'),
  ('threading',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/threading.py',
   'PYMODULE'),
  ('token',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/token.py',
   'PYMODULE'),
  ('tokenize',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/tokenize.py',
   'PYMODULE'),
  ('tracemalloc',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/tracemalloc.py',
   'PYMODULE'),
  ('tty', '/home/<USER>/anaconda3/envs/torch/lib/python3.9/tty.py', 'PYMODULE'),
  ('typing',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/typing.py',
   'PYMODULE'),
  ('typing_extensions',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/typing_extensions.py',
   'PYMODULE'),
  ('unittest',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/unittest/__init__.py',
   'PYMODULE'),
  ('unittest._log',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/unittest/_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/unittest/async_case.py',
   'PYMODULE'),
  ('unittest.case',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/unittest/case.py',
   'PYMODULE'),
  ('unittest.loader',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/unittest/loader.py',
   'PYMODULE'),
  ('unittest.main',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/unittest/main.py',
   'PYMODULE'),
  ('unittest.result',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/unittest/result.py',
   'PYMODULE'),
  ('unittest.runner',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/unittest/runner.py',
   'PYMODULE'),
  ('unittest.signals',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/unittest/signals.py',
   'PYMODULE'),
  ('unittest.suite',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/unittest/suite.py',
   'PYMODULE'),
  ('unittest.util',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/unittest/util.py',
   'PYMODULE'),
  ('urllib',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/urllib/__init__.py',
   'PYMODULE'),
  ('urllib.error',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/urllib/error.py',
   'PYMODULE'),
  ('urllib.parse',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/urllib/parse.py',
   'PYMODULE'),
  ('urllib.request',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/urllib/request.py',
   'PYMODULE'),
  ('urllib.response',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/urllib/response.py',
   'PYMODULE'),
  ('uu', '/home/<USER>/anaconda3/envs/torch/lib/python3.9/uu.py', 'PYMODULE'),
  ('webbrowser',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/webbrowser.py',
   'PYMODULE'),
  ('xml',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/xml/__init__.py',
   'PYMODULE'),
  ('xml.parsers',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/xml/parsers/__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/xml/parsers/expat.py',
   'PYMODULE'),
  ('xml.sax',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/xml/sax/__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/xml/sax/_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/xml/sax/expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/xml/sax/handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/xml/sax/saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/xml/sax/xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/xmlrpc/__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/xmlrpc/client.py',
   'PYMODULE'),
  ('zipfile',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/zipfile.py',
   'PYMODULE'),
  ('zipimport',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/zipimport.py',
   'PYMODULE')])

('/media/pyl/WD_Blue_1T/All_proj/Task-Series-Factory/build/merge_docx/merge_docx.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   '/media/pyl/WD_Blue_1T/All_proj/Task-Series-Factory/build/merge_docx/PYZ-00.pyz',
   'PYZ'),
  ('lib-dynload/_struct.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_struct.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/zlib.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/zlib.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('struct',
   '/media/pyl/WD_Blue_1T/All_proj/Task-Series-Factory/build/merge_docx/localpycs/struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   '/media/pyl/WD_Blue_1T/All_proj/Task-Series-Factory/build/merge_docx/localpycs/pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   '/media/pyl/WD_Blue_1T/All_proj/Task-Series-Factory/build/merge_docx/localpycs/pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   '/media/pyl/WD_Blue_1T/All_proj/Task-Series-Factory/build/merge_docx/localpycs/pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/PyInstaller/loader/pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/PyInstaller/hooks/rthooks/pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/PyInstaller/hooks/rthooks/pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('merge_docx',
   '/media/pyl/WD_Blue_1T/All_proj/Task-Series-Factory/merge_docx.py',
   'PYSOURCE'),
  ('libpython3.9.so.1.0',
   '/home/<USER>/anaconda3/envs/torch/lib/libpython3.9.so.1.0',
   'BINARY'),
  ('lib-dynload/math.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/math.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/select.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/select.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_posixsubprocess.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_posixsubprocess.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/grp.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/grp.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_datetime.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_datetime.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/unicodedata.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/unicodedata.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/array.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/array.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_socket.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_socket.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_statistics.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_statistics.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_contextvars.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_contextvars.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_decimal.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_decimal.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_pickle.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_pickle.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_hashlib.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_hashlib.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_sha3.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_sha3.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_blake2.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_blake2.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_sha256.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_sha256.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_md5.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_md5.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_sha1.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_sha1.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_sha512.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_sha512.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_random.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_random.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_bisect.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_bisect.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_csv.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_csv.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/binascii.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/binascii.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/resource.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/resource.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_lzma.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_lzma.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_bz2.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_bz2.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_posixshmem.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_posixshmem.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_multiprocessing.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_multiprocessing.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/pyexpat.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/pyexpat.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/termios.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/termios.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_ssl.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_ssl.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/mmap.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/mmap.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_ctypes.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_ctypes.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_queue.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_queue.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_opcode.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_opcode.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lxml/sax.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/sax.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lxml/objectify.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/objectify.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('charset_normalizer/md__mypyc.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/charset_normalizer/md__mypyc.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_multibytecodec.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_multibytecodec.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('charset_normalizer/md.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/charset_normalizer/md.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_json.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_json.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lxml/html/diff.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/html/diff.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_asyncio.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_asyncio.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/readline.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/readline.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lxml/builder.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/builder.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lxml/_elementpath.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/_elementpath.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lxml/etree.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/etree.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_jp.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_codecs_jp.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_kr.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_codecs_kr.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_iso2022.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_codecs_iso2022.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_cn.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_codecs_cn.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_tw.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_codecs_tw.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_hk.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_codecs_hk.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_heapq.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_heapq.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('libcrypto.so.3',
   '/home/<USER>/anaconda3/envs/torch/lib/libcrypto.so.3',
   'BINARY'),
  ('libz.so.1', '/home/<USER>/anaconda3/envs/torch/lib/libz.so.1', 'BINARY'),
  ('liblzma.so.5', '/home/<USER>/anaconda3/envs/torch/lib/liblzma.so.5', 'BINARY'),
  ('libssl.so.3', '/home/<USER>/anaconda3/envs/torch/lib/libssl.so.3', 'BINARY'),
  ('libffi.so.8', '/home/<USER>/anaconda3/envs/torch/lib/libffi.so.8', 'BINARY'),
  ('libtinfow.so.6',
   '/home/<USER>/anaconda3/envs/torch/lib/libtinfow.so.6',
   'BINARY'),
  ('libreadline.so.8',
   '/home/<USER>/anaconda3/envs/torch/lib/libreadline.so.8',
   'BINARY'),
  ('lxml/isoschematron/resources/xsl/RNG2Schtrn.xsl',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/isoschematron/resources/xsl/RNG2Schtrn.xsl',
   'DATA'),
  ('lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_dsdl_include.xsl',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_dsdl_include.xsl',
   'DATA'),
  ('lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_svrl_for_xslt1.xsl',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_svrl_for_xslt1.xsl',
   'DATA'),
  ('lxml/isoschematron/resources/xsl/iso-schematron-xslt1/readme.txt',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/isoschematron/resources/xsl/iso-schematron-xslt1/readme.txt',
   'DATA'),
  ('lxml/isoschematron/resources/rng/iso-schematron.rng',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/isoschematron/resources/rng/iso-schematron.rng',
   'DATA'),
  ('lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_abstract_expand.xsl',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_abstract_expand.xsl',
   'DATA'),
  ('lxml/isoschematron/resources/xsl/XSD2Schtrn.xsl',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/isoschematron/resources/xsl/XSD2Schtrn.xsl',
   'DATA'),
  ('lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_schematron_message.xsl',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_schematron_message.xsl',
   'DATA'),
  ('lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_schematron_skeleton_for_xslt1.xsl',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_schematron_skeleton_for_xslt1.xsl',
   'DATA'),
  ('docx/templates/default-docx-template/docProps/thumbnail.jpeg',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/templates/default-docx-template/docProps/thumbnail.jpeg',
   'DATA'),
  ('docx/templates/default-docx-template/word/fontTable.xml',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/templates/default-docx-template/word/fontTable.xml',
   'DATA'),
  ('docx/templates/default-docx-template/word/styles.xml',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/templates/default-docx-template/word/styles.xml',
   'DATA'),
  ('docx/templates/default-docx-template/word/numbering.xml',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/templates/default-docx-template/word/numbering.xml',
   'DATA'),
  ('docx/templates/default-docx-template/word/theme/theme1.xml',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/templates/default-docx-template/word/theme/theme1.xml',
   'DATA'),
  ('docx/templates/default-docx-template/word/document.xml',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/templates/default-docx-template/word/document.xml',
   'DATA'),
  ('docx/templates/default-header.xml',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/templates/default-header.xml',
   'DATA'),
  ('docx/templates/default-docx-template/word/webSettings.xml',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/templates/default-docx-template/word/webSettings.xml',
   'DATA'),
  ('docx/templates/default-docx-template/customXml/item1.xml',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/templates/default-docx-template/customXml/item1.xml',
   'DATA'),
  ('docx/templates/default-styles.xml',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/templates/default-styles.xml',
   'DATA'),
  ('docx/templates/default-docx-template/word/_rels/document.xml.rels',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/templates/default-docx-template/word/_rels/document.xml.rels',
   'DATA'),
  ('docx/templates/default-footer.xml',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/templates/default-footer.xml',
   'DATA'),
  ('docx/templates/default-docx-template/word/stylesWithEffects.xml',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/templates/default-docx-template/word/stylesWithEffects.xml',
   'DATA'),
  ('docx/templates/default.docx',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/templates/default.docx',
   'DATA'),
  ('docx/templates/default-docx-template/docProps/app.xml',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/templates/default-docx-template/docProps/app.xml',
   'DATA'),
  ('docx/py.typed',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/py.typed',
   'DATA'),
  ('docx/templates/default-docx-template/_rels/.rels',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/templates/default-docx-template/_rels/.rels',
   'DATA'),
  ('docx/templates/default-docx-template/customXml/itemProps1.xml',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/templates/default-docx-template/customXml/itemProps1.xml',
   'DATA'),
  ('docx/templates/default-docx-template/word/settings.xml',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/templates/default-docx-template/word/settings.xml',
   'DATA'),
  ('docx/templates/default-settings.xml',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/templates/default-settings.xml',
   'DATA'),
  ('docx/templates/default-docx-template/[Content_Types].xml',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/templates/default-docx-template/[Content_Types].xml',
   'DATA'),
  ('docx/templates/default-docx-template/docProps/core.xml',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/templates/default-docx-template/docProps/core.xml',
   'DATA'),
  ('docx/templates/default-docx-template/customXml/_rels/item1.xml.rels',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/templates/default-docx-template/customXml/_rels/item1.xml.rels',
   'DATA'),
  ('base_library.zip',
   '/media/pyl/WD_Blue_1T/All_proj/Task-Series-Factory/build/merge_docx/base_library.zip',
   'DATA')],
 'libpython3.9.so.1.0',
 False,
 False,
 False,
 [],
 None,
 None,
 None)

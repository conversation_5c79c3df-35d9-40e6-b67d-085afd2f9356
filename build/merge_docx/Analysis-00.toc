(['/media/pyl/WD_Blue_1T/All_proj/Task-Series-Factory/merge_docx.py'],
 ['/media/pyl/WD_Blue_1T/All_proj/Task-Series-Factory'],
 ['lxml'],
 [('/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/numpy/_pyinstaller',
   0),
  ('/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/_pyinstaller_hooks_contrib/stdhooks',
   -1000),
  ('/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [],
 '3.9.19 (main, May  6 2024, 19:43:03) \n[GCC 11.2.0]',
 [('pyi_rth_inspect',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/PyInstaller/hooks/rthooks/pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/PyInstaller/hooks/rthooks/pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('merge_docx',
   '/media/pyl/WD_Blue_1T/All_proj/Task-Series-Factory/merge_docx.py',
   'PYSOURCE')],
 [('subprocess',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/subprocess.py',
   'PYMODULE'),
  ('selectors',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/selectors.py',
   'PYMODULE'),
  ('contextlib',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/contextlib.py',
   'PYMODULE'),
  ('threading',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/threading.py',
   'PYMODULE'),
  ('_threading_local',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/_threading_local.py',
   'PYMODULE'),
  ('signal',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/signal.py',
   'PYMODULE'),
  ('_strptime',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/_strptime.py',
   'PYMODULE'),
  ('datetime',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/datetime.py',
   'PYMODULE'),
  ('calendar',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/calendar.py',
   'PYMODULE'),
  ('argparse',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/argparse.py',
   'PYMODULE'),
  ('textwrap',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/textwrap.py',
   'PYMODULE'),
  ('shutil',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/shutil.py',
   'PYMODULE'),
  ('zipfile',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/zipfile.py',
   'PYMODULE'),
  ('py_compile',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/py_compile.py',
   'PYMODULE'),
  ('importlib.machinery',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/importlib/machinery.py',
   'PYMODULE'),
  ('importlib',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/importlib/__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/importlib/_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/importlib/_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/importlib/metadata.py',
   'PYMODULE'),
  ('importlib.abc',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/importlib/abc.py',
   'PYMODULE'),
  ('typing',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/typing.py',
   'PYMODULE'),
  ('configparser',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/configparser.py',
   'PYMODULE'),
  ('pathlib',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/pathlib.py',
   'PYMODULE'),
  ('urllib.parse',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/urllib/parse.py',
   'PYMODULE'),
  ('urllib',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/urllib/__init__.py',
   'PYMODULE'),
  ('email',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/email/__init__.py',
   'PYMODULE'),
  ('email.parser',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/email/parser.py',
   'PYMODULE'),
  ('email._policybase',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/email/_policybase.py',
   'PYMODULE'),
  ('email.utils',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/email/utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/email/_parseaddr.py',
   'PYMODULE'),
  ('socket',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/socket.py',
   'PYMODULE'),
  ('random',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/random.py',
   'PYMODULE'),
  ('statistics',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/statistics.py',
   'PYMODULE'),
  ('decimal',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/contextvars.py',
   'PYMODULE'),
  ('fractions',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/fractions.py',
   'PYMODULE'),
  ('numbers',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/numbers.py',
   'PYMODULE'),
  ('hashlib',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/hashlib.py',
   'PYMODULE'),
  ('logging',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/logging/__init__.py',
   'PYMODULE'),
  ('pickle',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/pickle.py',
   'PYMODULE'),
  ('pprint',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/pprint.py',
   'PYMODULE'),
  ('_compat_pickle',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/_compat_pickle.py',
   'PYMODULE'),
  ('string',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/string.py',
   'PYMODULE'),
  ('bisect',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/bisect.py',
   'PYMODULE'),
  ('email.feedparser',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/email/feedparser.py',
   'PYMODULE'),
  ('email.message',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/email/message.py',
   'PYMODULE'),
  ('email.policy',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/email/policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/email/contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/email/quoprimime.py',
   'PYMODULE'),
  ('email.headerregistry',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/email/headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/email/iterators.py',
   'PYMODULE'),
  ('email.generator',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/email/generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/email/_encoded_words.py',
   'PYMODULE'),
  ('base64',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/base64.py',
   'PYMODULE'),
  ('getopt',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/getopt.py',
   'PYMODULE'),
  ('quopri',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/quopri.py',
   'PYMODULE'),
  ('uu', '/home/<USER>/anaconda3/envs/torch/lib/python3.9/uu.py', 'PYMODULE'),
  ('optparse',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/optparse.py',
   'PYMODULE'),
  ('email._header_value_parser',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/email/_header_value_parser.py',
   'PYMODULE'),
  ('email.header',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/email/header.py',
   'PYMODULE'),
  ('email.base64mime',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/email/base64mime.py',
   'PYMODULE'),
  ('email.charset',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/email/charset.py',
   'PYMODULE'),
  ('email.encoders',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/email/encoders.py',
   'PYMODULE'),
  ('email.errors',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/email/errors.py',
   'PYMODULE'),
  ('csv', '/home/<USER>/anaconda3/envs/torch/lib/python3.9/csv.py', 'PYMODULE'),
  ('tokenize',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/tokenize.py',
   'PYMODULE'),
  ('token',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/token.py',
   'PYMODULE'),
  ('struct',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/struct.py',
   'PYMODULE'),
  ('importlib.util',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/importlib/util.py',
   'PYMODULE'),
  ('tarfile',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/tarfile.py',
   'PYMODULE'),
  ('gzip', '/home/<USER>/anaconda3/envs/torch/lib/python3.9/gzip.py', 'PYMODULE'),
  ('_compression',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/_compression.py',
   'PYMODULE'),
  ('lzma', '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lzma.py', 'PYMODULE'),
  ('bz2', '/home/<USER>/anaconda3/envs/torch/lib/python3.9/bz2.py', 'PYMODULE'),
  ('fnmatch',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/fnmatch.py',
   'PYMODULE'),
  ('copy', '/home/<USER>/anaconda3/envs/torch/lib/python3.9/copy.py', 'PYMODULE'),
  ('gettext',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/gettext.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/multiprocessing/spawn.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/multiprocessing/resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.util',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/multiprocessing/util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/multiprocessing/forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/multiprocessing/connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/multiprocessing/resource_sharer.py',
   'PYMODULE'),
  ('xmlrpc.client',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/xmlrpc/client.py',
   'PYMODULE'),
  ('xmlrpc',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/xmlrpc/__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/xml/parsers/expat.py',
   'PYMODULE'),
  ('xml.parsers',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/xml/parsers/__init__.py',
   'PYMODULE'),
  ('xml',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/xml/__init__.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/xml/sax/expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/xml/sax/saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/urllib/request.py',
   'PYMODULE'),
  ('ipaddress',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/ipaddress.py',
   'PYMODULE'),
  ('getpass',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/getpass.py',
   'PYMODULE'),
  ('nturl2path',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/nturl2path.py',
   'PYMODULE'),
  ('ftplib',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/ftplib.py',
   'PYMODULE'),
  ('netrc',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/netrc.py',
   'PYMODULE'),
  ('shlex',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/shlex.py',
   'PYMODULE'),
  ('mimetypes',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/mimetypes.py',
   'PYMODULE'),
  ('http.cookiejar',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/http/cookiejar.py',
   'PYMODULE'),
  ('http',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/http/__init__.py',
   'PYMODULE'),
  ('ssl', '/home/<USER>/anaconda3/envs/torch/lib/python3.9/ssl.py', 'PYMODULE'),
  ('urllib.response',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/urllib/response.py',
   'PYMODULE'),
  ('urllib.error',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/urllib/error.py',
   'PYMODULE'),
  ('xml.sax',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/xml/sax/__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/xml/sax/handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/xml/sax/_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/xml/sax/xmlreader.py',
   'PYMODULE'),
  ('http.client',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/http/client.py',
   'PYMODULE'),
  ('hmac', '/home/<USER>/anaconda3/envs/torch/lib/python3.9/hmac.py', 'PYMODULE'),
  ('tempfile',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/tempfile.py',
   'PYMODULE'),
  ('multiprocessing.context',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/multiprocessing/context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/multiprocessing/popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/multiprocessing/popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/multiprocessing/popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/multiprocessing/popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/multiprocessing/sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/multiprocessing/heap.py',
   'PYMODULE'),
  ('ctypes',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/ctypes/__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/ctypes/_endian.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/multiprocessing/pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/multiprocessing/dummy/__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/multiprocessing/dummy/connection.py',
   'PYMODULE'),
  ('queue',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/queue.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/multiprocessing/queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/multiprocessing/synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/multiprocessing/managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/multiprocessing/shared_memory.py',
   'PYMODULE'),
  ('secrets',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/secrets.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/multiprocessing/reduction.py',
   'PYMODULE'),
  ('multiprocessing.process',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/multiprocessing/process.py',
   'PYMODULE'),
  ('runpy',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/runpy.py',
   'PYMODULE'),
  ('pkgutil',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/pkgutil.py',
   'PYMODULE'),
  ('zipimport',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/zipimport.py',
   'PYMODULE'),
  ('inspect',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/inspect.py',
   'PYMODULE'),
  ('dis', '/home/<USER>/anaconda3/envs/torch/lib/python3.9/dis.py', 'PYMODULE'),
  ('opcode',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/opcode.py',
   'PYMODULE'),
  ('ast', '/home/<USER>/anaconda3/envs/torch/lib/python3.9/ast.py', 'PYMODULE'),
  ('multiprocessing',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/multiprocessing/__init__.py',
   'PYMODULE'),
  ('lxml',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/__init__.py',
   'PYMODULE'),
  ('lxml.usedoctest',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/usedoctest.py',
   'PYMODULE'),
  ('lxml.pyclasslookup',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/pyclasslookup.py',
   'PYMODULE'),
  ('lxml.isoschematron',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/isoschematron/__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxslt',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/includes/libxslt/__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxml',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/includes/libxml/__init__.py',
   'PYMODULE'),
  ('lxml.includes.libexslt',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/includes/libexslt/__init__.py',
   'PYMODULE'),
  ('lxml.includes.extlibs',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/includes/extlibs/__init__.py',
   'PYMODULE'),
  ('lxml.includes',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/includes/__init__.py',
   'PYMODULE'),
  ('lxml.html.usedoctest',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/html/usedoctest.py',
   'PYMODULE'),
  ('lxml.html.soupparser',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/html/soupparser.py',
   'PYMODULE'),
  ('html.entities',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/html/entities.py',
   'PYMODULE'),
  ('html',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/html/__init__.py',
   'PYMODULE'),
  ('bs4',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/bs4/__init__.py',
   'PYMODULE'),
  ('bs4._warnings',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/bs4/_warnings.py',
   'PYMODULE'),
  ('bs4.exceptions',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/bs4/exceptions.py',
   'PYMODULE'),
  ('bs4._typing',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/bs4/_typing.py',
   'PYMODULE'),
  ('typing_extensions',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/typing_extensions.py',
   'PYMODULE'),
  ('bs4.filter',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/bs4/filter.py',
   'PYMODULE'),
  ('__future__',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/__future__.py',
   'PYMODULE'),
  ('bs4.formatter',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/bs4/formatter.py',
   'PYMODULE'),
  ('bs4.element',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/bs4/element.py',
   'PYMODULE'),
  ('bs4._deprecation',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/bs4/_deprecation.py',
   'PYMODULE'),
  ('bs4.css',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/bs4/css.py',
   'PYMODULE'),
  ('soupsieve',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/soupsieve/__init__.py',
   'PYMODULE'),
  ('soupsieve.css_parser',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/soupsieve/css_parser.py',
   'PYMODULE'),
  ('soupsieve.css_match',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/soupsieve/css_match.py',
   'PYMODULE'),
  ('soupsieve.css_types',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/soupsieve/css_types.py',
   'PYMODULE'),
  ('soupsieve.pretty',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/soupsieve/pretty.py',
   'PYMODULE'),
  ('soupsieve.util',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/soupsieve/util.py',
   'PYMODULE'),
  ('soupsieve.__meta__',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/soupsieve/__meta__.py',
   'PYMODULE'),
  ('bs4.dammit',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/bs4/dammit.py',
   'PYMODULE'),
  ('charset_normalizer',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/charset_normalizer/__init__.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/charset_normalizer/version.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/charset_normalizer/utils.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/charset_normalizer/constant.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/charset_normalizer/models.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/charset_normalizer/cd.py',
   'PYMODULE'),
  ('json',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/json/__init__.py',
   'PYMODULE'),
  ('json.encoder',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/json/encoder.py',
   'PYMODULE'),
  ('json.decoder',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/json/decoder.py',
   'PYMODULE'),
  ('json.scanner',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/json/scanner.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/charset_normalizer/legacy.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/charset_normalizer/api.py',
   'PYMODULE'),
  ('bs4.builder._htmlparser',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/bs4/builder/_htmlparser.py',
   'PYMODULE'),
  ('html.parser',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/html/parser.py',
   'PYMODULE'),
  ('_markupbase',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/_markupbase.py',
   'PYMODULE'),
  ('bs4.builder',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/bs4/builder/__init__.py',
   'PYMODULE'),
  ('bs4.builder._lxml',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/bs4/builder/_lxml.py',
   'PYMODULE'),
  ('bs4.builder._html5lib',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/bs4/builder/_html5lib.py',
   'PYMODULE'),
  ('lxml.html.html5parser',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/html/html5parser.py',
   'PYMODULE'),
  ('lxml.html.formfill',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/html/formfill.py',
   'PYMODULE'),
  ('cgi', '/home/<USER>/anaconda3/envs/torch/lib/python3.9/cgi.py', 'PYMODULE'),
  ('difflib',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/difflib.py',
   'PYMODULE'),
  ('lxml.html.defs',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/html/defs.py',
   'PYMODULE'),
  ('lxml.html.clean',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/html/clean.py',
   'PYMODULE'),
  ('lxml.html.builder',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/html/builder.py',
   'PYMODULE'),
  ('lxml.html._setmixin',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/html/_setmixin.py',
   'PYMODULE'),
  ('lxml.html._html5builder',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/html/_html5builder.py',
   'PYMODULE'),
  ('lxml.html._diffcommand',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/html/_diffcommand.py',
   'PYMODULE'),
  ('lxml.html.ElementSoup',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/html/ElementSoup.py',
   'PYMODULE'),
  ('lxml.html',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/html/__init__.py',
   'PYMODULE'),
  ('webbrowser',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/webbrowser.py',
   'PYMODULE'),
  ('glob', '/home/<USER>/anaconda3/envs/torch/lib/python3.9/glob.py', 'PYMODULE'),
  ('lxml.doctestcompare',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/doctestcompare.py',
   'PYMODULE'),
  ('doctest',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/doctest.py',
   'PYMODULE'),
  ('unittest',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/unittest/__init__.py',
   'PYMODULE'),
  ('unittest.async_case',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/unittest/async_case.py',
   'PYMODULE'),
  ('asyncio',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/unix_events.py',
   'PYMODULE'),
  ('asyncio.log',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/log.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/threads.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/streams.py',
   'PYMODULE'),
  ('asyncio.queues',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/runners.py',
   'PYMODULE'),
  ('asyncio.base_events',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/base_events.py',
   'PYMODULE'),
  ('concurrent.futures',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/concurrent/futures/__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/concurrent/futures/thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/concurrent/futures/process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/concurrent/futures/_base.py',
   'PYMODULE'),
  ('concurrent',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/concurrent/__init__.py',
   'PYMODULE'),
  ('asyncio.trsock',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/staggered.py',
   'PYMODULE'),
  ('asyncio.tasks',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/tasks.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/locks.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/exceptions.py',
   'PYMODULE'),
  ('asyncio.events',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/events.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/coroutines.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/base_futures.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/asyncio/constants.py',
   'PYMODULE'),
  ('unittest.signals',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/unittest/signals.py',
   'PYMODULE'),
  ('unittest.main',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/unittest/main.py',
   'PYMODULE'),
  ('unittest.runner',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/unittest/runner.py',
   'PYMODULE'),
  ('unittest.loader',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/unittest/loader.py',
   'PYMODULE'),
  ('unittest.suite',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/unittest/suite.py',
   'PYMODULE'),
  ('unittest.case',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/unittest/case.py',
   'PYMODULE'),
  ('unittest._log',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/unittest/_log.py',
   'PYMODULE'),
  ('unittest.result',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/unittest/result.py',
   'PYMODULE'),
  ('unittest.util',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/unittest/util.py',
   'PYMODULE'),
  ('pdb', '/home/<USER>/anaconda3/envs/torch/lib/python3.9/pdb.py', 'PYMODULE'),
  ('pydoc',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/pydoc.py',
   'PYMODULE'),
  ('http.server',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/http/server.py',
   'PYMODULE'),
  ('socketserver',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/socketserver.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/pydoc_data/topics.py',
   'PYMODULE'),
  ('pydoc_data',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/pydoc_data/__init__.py',
   'PYMODULE'),
  ('tty', '/home/<USER>/anaconda3/envs/torch/lib/python3.9/tty.py', 'PYMODULE'),
  ('sysconfig',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/sysconfig.py',
   'PYMODULE'),
  ('_sysconfigdata__linux_x86_64-linux-gnu',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/_sysconfigdata__linux_x86_64-linux-gnu.py',
   'PYMODULE'),
  ('_aix_support',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/_bootsubprocess.py',
   'PYMODULE'),
  ('platform',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/platform.py',
   'PYMODULE'),
  ('code', '/home/<USER>/anaconda3/envs/torch/lib/python3.9/code.py', 'PYMODULE'),
  ('codeop',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/codeop.py',
   'PYMODULE'),
  ('bdb', '/home/<USER>/anaconda3/envs/torch/lib/python3.9/bdb.py', 'PYMODULE'),
  ('cmd', '/home/<USER>/anaconda3/envs/torch/lib/python3.9/cmd.py', 'PYMODULE'),
  ('lxml.cssselect',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/cssselect.py',
   'PYMODULE'),
  ('lxml.ElementInclude',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/ElementInclude.py',
   'PYMODULE'),
  ('stringprep',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/stringprep.py',
   'PYMODULE'),
  ('_py_abc',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/_py_abc.py',
   'PYMODULE'),
  ('tracemalloc',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/tracemalloc.py',
   'PYMODULE'),
  ('docx',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/__init__.py',
   'PYMODULE'),
  ('docx.parts.styles',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/parts/styles.py',
   'PYMODULE'),
  ('docx.parts',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/parts/__init__.py',
   'PYMODULE'),
  ('docx.opc.package',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/opc/package.py',
   'PYMODULE'),
  ('docx.opc.coreprops',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/opc/coreprops.py',
   'PYMODULE'),
  ('docx.oxml.coreprops',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/oxml/coreprops.py',
   'PYMODULE'),
  ('docx.oxml',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/oxml/__init__.py',
   'PYMODULE'),
  ('docx.oxml.text.parfmt',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/oxml/text/parfmt.py',
   'PYMODULE'),
  ('docx.oxml.text',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/oxml/text/__init__.py',
   'PYMODULE'),
  ('docx.oxml.simpletypes',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/oxml/simpletypes.py',
   'PYMODULE'),
  ('docx.exceptions',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/exceptions.py',
   'PYMODULE'),
  ('docx.enum.text',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/enum/text.py',
   'PYMODULE'),
  ('docx.enum',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/enum/__init__.py',
   'PYMODULE'),
  ('docx.enum.base',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/enum/base.py',
   'PYMODULE'),
  ('docx.oxml.text.paragraph',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/oxml/text/paragraph.py',
   'PYMODULE'),
  ('docx.oxml.text.font',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/oxml/text/font.py',
   'PYMODULE'),
  ('docx.enum.dml',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/enum/dml.py',
   'PYMODULE'),
  ('docx.oxml.table',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/oxml/table.py',
   'PYMODULE'),
  ('docx.enum.table',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/enum/table.py',
   'PYMODULE'),
  ('docx.oxml.styles',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/oxml/styles.py',
   'PYMODULE'),
  ('docx.enum.style',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/enum/style.py',
   'PYMODULE'),
  ('docx.oxml.settings',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/oxml/settings.py',
   'PYMODULE'),
  ('docx.oxml.section',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/oxml/section.py',
   'PYMODULE'),
  ('docx.enum.section',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/enum/section.py',
   'PYMODULE'),
  ('docx.oxml.numbering',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/oxml/numbering.py',
   'PYMODULE'),
  ('docx.oxml.document',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/oxml/document.py',
   'PYMODULE'),
  ('docx.oxml.text.run',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/oxml/text/run.py',
   'PYMODULE'),
  ('docx.oxml.text.pagebreak',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/oxml/text/pagebreak.py',
   'PYMODULE'),
  ('docx.oxml.text.hyperlink',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/oxml/text/hyperlink.py',
   'PYMODULE'),
  ('docx.oxml.shared',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/oxml/shared.py',
   'PYMODULE'),
  ('docx.oxml.shape',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/oxml/shape.py',
   'PYMODULE'),
  ('docx.oxml.drawing',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/oxml/drawing.py',
   'PYMODULE'),
  ('docx.oxml.xmlchemy',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/oxml/xmlchemy.py',
   'PYMODULE'),
  ('docx.oxml.exceptions',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/oxml/exceptions.py',
   'PYMODULE'),
  ('docx.oxml.ns',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/oxml/ns.py',
   'PYMODULE'),
  ('docx.shared',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/shared.py',
   'PYMODULE'),
  ('docx.parts.story',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/parts/story.py',
   'PYMODULE'),
  ('docx.styles.style',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/styles/style.py',
   'PYMODULE'),
  ('docx.text.parfmt',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/text/parfmt.py',
   'PYMODULE'),
  ('docx.text',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/text/__init__.py',
   'PYMODULE'),
  ('docx.text.tabstops',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/text/tabstops.py',
   'PYMODULE'),
  ('docx.text.font',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/text/font.py',
   'PYMODULE'),
  ('docx.dml.color',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/dml/color.py',
   'PYMODULE'),
  ('docx.dml',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/dml/__init__.py',
   'PYMODULE'),
  ('docx.styles',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/styles/__init__.py',
   'PYMODULE'),
  ('docx.image.image',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/image/image.py',
   'PYMODULE'),
  ('docx.image',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/image/__init__.py',
   'PYMODULE'),
  ('docx.image.tiff',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/image/tiff.py',
   'PYMODULE'),
  ('docx.image.helpers',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/image/helpers.py',
   'PYMODULE'),
  ('docx.image.constants',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/image/constants.py',
   'PYMODULE'),
  ('docx.image.png',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/image/png.py',
   'PYMODULE'),
  ('docx.image.jpeg',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/image/jpeg.py',
   'PYMODULE'),
  ('docx.image.gif',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/image/gif.py',
   'PYMODULE'),
  ('docx.image.bmp',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/image/bmp.py',
   'PYMODULE'),
  ('docx.image.exceptions',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/image/exceptions.py',
   'PYMODULE'),
  ('docx.types',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/types.py',
   'PYMODULE'),
  ('docx.opc.rel',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/opc/rel.py',
   'PYMODULE'),
  ('docx.opc.oxml',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/opc/oxml.py',
   'PYMODULE'),
  ('docx.opc.pkgwriter',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/opc/pkgwriter.py',
   'PYMODULE'),
  ('docx.opc.spec',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/opc/spec.py',
   'PYMODULE'),
  ('docx.opc.shared',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/opc/shared.py',
   'PYMODULE'),
  ('docx.opc.phys_pkg',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/opc/phys_pkg.py',
   'PYMODULE'),
  ('docx.opc.exceptions',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/opc/exceptions.py',
   'PYMODULE'),
  ('docx.opc.pkgreader',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/opc/pkgreader.py',
   'PYMODULE'),
  ('docx.opc',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/opc/__init__.py',
   'PYMODULE'),
  ('docx.styles.styles',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/styles/styles.py',
   'PYMODULE'),
  ('docx.styles.latent',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/styles/latent.py',
   'PYMODULE'),
  ('docx.oxml.parser',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/oxml/parser.py',
   'PYMODULE'),
  ('docx.opc.packuri',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/opc/packuri.py',
   'PYMODULE'),
  ('docx.parts.settings',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/parts/settings.py',
   'PYMODULE'),
  ('docx.package',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/package.py',
   'PYMODULE'),
  ('docx.settings',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/settings.py',
   'PYMODULE'),
  ('docx.parts.numbering',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/parts/numbering.py',
   'PYMODULE'),
  ('docx.parts.image',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/parts/image.py',
   'PYMODULE'),
  ('docx.parts.hdrftr',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/parts/hdrftr.py',
   'PYMODULE'),
  ('docx.parts.document',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/parts/document.py',
   'PYMODULE'),
  ('docx.shape',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/shape.py',
   'PYMODULE'),
  ('docx.enum.shape',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/enum/shape.py',
   'PYMODULE'),
  ('docx.document',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/document.py',
   'PYMODULE'),
  ('docx.text.paragraph',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/text/paragraph.py',
   'PYMODULE'),
  ('docx.text.run',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/text/run.py',
   'PYMODULE'),
  ('docx.drawing',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/drawing/__init__.py',
   'PYMODULE'),
  ('docx.text.pagebreak',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/text/pagebreak.py',
   'PYMODULE'),
  ('docx.text.hyperlink',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/text/hyperlink.py',
   'PYMODULE'),
  ('docx.table',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/table.py',
   'PYMODULE'),
  ('docx.section',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/section.py',
   'PYMODULE'),
  ('docx.blkcntnr',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/blkcntnr.py',
   'PYMODULE'),
  ('docx.opc.parts.coreprops',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/opc/parts/coreprops.py',
   'PYMODULE'),
  ('docx.opc.parts',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/opc/parts/__init__.py',
   'PYMODULE'),
  ('docx.opc.constants',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/opc/constants.py',
   'PYMODULE'),
  ('docx.opc.part',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/opc/part.py',
   'PYMODULE'),
  ('docx.api',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/api.py',
   'PYMODULE')],
 [('libpython3.9.so.1.0',
   '/home/<USER>/anaconda3/envs/torch/lib/libpython3.9.so.1.0',
   'BINARY'),
  ('lib-dynload/math.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/math.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/select.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/select.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_posixsubprocess.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_posixsubprocess.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/grp.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/grp.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_datetime.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_datetime.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/unicodedata.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/unicodedata.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/array.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/array.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_socket.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_socket.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_statistics.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_statistics.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_contextvars.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_contextvars.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_decimal.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_decimal.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_pickle.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_pickle.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_hashlib.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_hashlib.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_sha3.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_sha3.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_blake2.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_blake2.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_sha256.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_sha256.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_md5.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_md5.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_sha1.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_sha1.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_sha512.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_sha512.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_random.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_random.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_bisect.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_bisect.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_csv.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_csv.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_struct.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_struct.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/binascii.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/binascii.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/resource.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/resource.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_lzma.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_lzma.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_bz2.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_bz2.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/zlib.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/zlib.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_posixshmem.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_posixshmem.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_multiprocessing.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_multiprocessing.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/pyexpat.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/pyexpat.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/termios.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/termios.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_ssl.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_ssl.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/mmap.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/mmap.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_ctypes.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_ctypes.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_queue.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_queue.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_opcode.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_opcode.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lxml/sax.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/sax.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lxml/objectify.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/objectify.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('charset_normalizer/md__mypyc.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/charset_normalizer/md__mypyc.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_multibytecodec.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_multibytecodec.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('charset_normalizer/md.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/charset_normalizer/md.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_json.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_json.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lxml/html/diff.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/html/diff.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_asyncio.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_asyncio.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/readline.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/readline.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lxml/builder.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/builder.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lxml/_elementpath.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/_elementpath.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lxml/etree.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/etree.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_jp.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_codecs_jp.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_kr.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_codecs_kr.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_iso2022.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_codecs_iso2022.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_cn.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_codecs_cn.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_tw.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_codecs_tw.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_hk.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_codecs_hk.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_heapq.cpython-39-x86_64-linux-gnu.so',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/lib-dynload/_heapq.cpython-39-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('libcrypto.so.3',
   '/home/<USER>/anaconda3/envs/torch/lib/libcrypto.so.3',
   'BINARY'),
  ('libz.so.1', '/home/<USER>/anaconda3/envs/torch/lib/libz.so.1', 'BINARY'),
  ('liblzma.so.5', '/home/<USER>/anaconda3/envs/torch/lib/liblzma.so.5', 'BINARY'),
  ('libssl.so.3', '/home/<USER>/anaconda3/envs/torch/lib/libssl.so.3', 'BINARY'),
  ('libffi.so.8', '/home/<USER>/anaconda3/envs/torch/lib/libffi.so.8', 'BINARY'),
  ('libtinfow.so.6',
   '/home/<USER>/anaconda3/envs/torch/lib/libtinfow.so.6',
   'BINARY'),
  ('libreadline.so.8',
   '/home/<USER>/anaconda3/envs/torch/lib/libreadline.so.8',
   'BINARY')],
 [],
 [],
 [('lxml/isoschematron/resources/xsl/RNG2Schtrn.xsl',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/isoschematron/resources/xsl/RNG2Schtrn.xsl',
   'DATA'),
  ('lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_dsdl_include.xsl',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_dsdl_include.xsl',
   'DATA'),
  ('lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_svrl_for_xslt1.xsl',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_svrl_for_xslt1.xsl',
   'DATA'),
  ('lxml/isoschematron/resources/xsl/iso-schematron-xslt1/readme.txt',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/isoschematron/resources/xsl/iso-schematron-xslt1/readme.txt',
   'DATA'),
  ('lxml/isoschematron/resources/rng/iso-schematron.rng',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/isoschematron/resources/rng/iso-schematron.rng',
   'DATA'),
  ('lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_abstract_expand.xsl',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_abstract_expand.xsl',
   'DATA'),
  ('lxml/isoschematron/resources/xsl/XSD2Schtrn.xsl',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/isoschematron/resources/xsl/XSD2Schtrn.xsl',
   'DATA'),
  ('lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_schematron_message.xsl',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_schematron_message.xsl',
   'DATA'),
  ('lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_schematron_skeleton_for_xslt1.xsl',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_schematron_skeleton_for_xslt1.xsl',
   'DATA'),
  ('docx/templates/default-docx-template/docProps/thumbnail.jpeg',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/templates/default-docx-template/docProps/thumbnail.jpeg',
   'DATA'),
  ('docx/templates/default-docx-template/word/fontTable.xml',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/templates/default-docx-template/word/fontTable.xml',
   'DATA'),
  ('docx/templates/default-docx-template/word/styles.xml',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/templates/default-docx-template/word/styles.xml',
   'DATA'),
  ('docx/templates/default-docx-template/word/numbering.xml',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/templates/default-docx-template/word/numbering.xml',
   'DATA'),
  ('docx/templates/default-docx-template/word/theme/theme1.xml',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/templates/default-docx-template/word/theme/theme1.xml',
   'DATA'),
  ('docx/templates/default-docx-template/word/document.xml',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/templates/default-docx-template/word/document.xml',
   'DATA'),
  ('docx/templates/default-header.xml',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/templates/default-header.xml',
   'DATA'),
  ('docx/templates/default-docx-template/word/webSettings.xml',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/templates/default-docx-template/word/webSettings.xml',
   'DATA'),
  ('docx/templates/default-docx-template/customXml/item1.xml',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/templates/default-docx-template/customXml/item1.xml',
   'DATA'),
  ('docx/templates/default-styles.xml',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/templates/default-styles.xml',
   'DATA'),
  ('docx/templates/default-docx-template/word/_rels/document.xml.rels',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/templates/default-docx-template/word/_rels/document.xml.rels',
   'DATA'),
  ('docx/templates/default-footer.xml',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/templates/default-footer.xml',
   'DATA'),
  ('docx/templates/default-docx-template/word/stylesWithEffects.xml',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/templates/default-docx-template/word/stylesWithEffects.xml',
   'DATA'),
  ('docx/templates/default.docx',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/templates/default.docx',
   'DATA'),
  ('docx/templates/default-docx-template/docProps/app.xml',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/templates/default-docx-template/docProps/app.xml',
   'DATA'),
  ('docx/py.typed',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/py.typed',
   'DATA'),
  ('docx/templates/default-docx-template/_rels/.rels',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/templates/default-docx-template/_rels/.rels',
   'DATA'),
  ('docx/templates/default-docx-template/customXml/itemProps1.xml',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/templates/default-docx-template/customXml/itemProps1.xml',
   'DATA'),
  ('docx/templates/default-docx-template/word/settings.xml',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/templates/default-docx-template/word/settings.xml',
   'DATA'),
  ('docx/templates/default-settings.xml',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/templates/default-settings.xml',
   'DATA'),
  ('docx/templates/default-docx-template/[Content_Types].xml',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/templates/default-docx-template/[Content_Types].xml',
   'DATA'),
  ('docx/templates/default-docx-template/docProps/core.xml',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/templates/default-docx-template/docProps/core.xml',
   'DATA'),
  ('docx/templates/default-docx-template/customXml/_rels/item1.xml.rels',
   '/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/docx/templates/default-docx-template/customXml/_rels/item1.xml.rels',
   'DATA'),
  ('base_library.zip',
   '/media/pyl/WD_Blue_1T/All_proj/Task-Series-Factory/build/merge_docx/base_library.zip',
   'DATA')])

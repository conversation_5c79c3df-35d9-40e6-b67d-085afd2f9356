# -*-coding:utf-8-*-
"""
created by @Moss 2023/08/07
数据2.5质量评估   --全面性
Abstract：
    读取数据更新记录表，统计(1)各个学校在该项目下所有学校的数量占比；
                         (2)各个学校下的当年各个月份的数据量占比；
Usage：
    python
"""

import pandas as pd
import numpy as np
from pathlib import Path
from collections import Counter

from base import BaseReader



def Read_Excel_mod(func):
    def warpper(self, *args, **kwargs):
        # pandas read the excel file and its sub sheet
        all_sheet = pd.read_excel(self.file)

        func(self, all_sheet, *args,**kwargs)       # 调用原始函数，传入参数

    return warpper



class excel_Reader(BaseReader):
    def __init__(self, files):
        self.files = []
        excel = ['xls', 'xlsx', 'xlsm', 'odf', 'ods', 'odt']

        super().__init__(fileStyle=excel, file_path=files)


    def file_Reader(self, file):
        """
        pandas read the excel file and its sub sheet

        :return:
        """
        df = pd.read_excel(file, header=[0, 1])


        return df

    def raed_onefile(self):
        all_sheet = pd.read_excel(self.file_pth, sheet_name=None, header=None)       # read all sheet and its sub sheet
        for label,df in all_sheet.items():
            sample_dir = Path(self.file_pth).parent
            (sample_dir / Path(label)).mkdir(exist_ok=True)
            for sample in df.iloc[:,0]:
                src1 = sample_dir / Path(sample)
                src2 = str(src1.with_suffix('.png'))
                # src3 = str(src1.with_suffix('.txt'))          # 读取到txt文件

                # Cut
                # Path(src3).rename(sample_dir / Path(label) / Path(src3).name)
                Path(src2).rename(sample_dir / Path(label) / Path(src2).name)
                Path(src1).rename(sample_dir / Path(label) / Path(src1).name)


        return


    def __next__(self):
        """
        :return: 读取(后处理)的pandas数据, 原始文件路径
        """
        # if self.nf == 1:
        #     self.file_Reader(self.files[0])     # 仅有一个excel表时，[file]
        if self.count == self.nf:
            raise StopIteration
        path = self.files[self.count]
        # read and processing
        df = self.file_Reader(path)

        self.count += 1

        return df, path

    def __iter__(self):
        self.count = 1
        return self

    def __len__(self):
        return self.nf




def _add_toDict(sub_set_scl, scl, season, num):
    if scl not in sub_set_scl:
        sub_set_scl[scl] = {'A':0, 'B':0, 'C':0, 'D':0}
    sub_set_scl[scl][season] += num
    sub_set_scl[scl][season] = np.round(sub_set_scl[scl][season], decimals=2)

    return sub_set_scl








if __name__ == '__main__':
    files = '/root/persons/ai_group/Moss/test/外包分类结果/1125/test_class_douluo_YSExcel.xlsx'

    datasets = excel_Reader(files=files)
    datasets.raed_onefile()




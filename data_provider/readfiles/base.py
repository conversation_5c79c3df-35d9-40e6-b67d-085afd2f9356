# -*-coding:utf-8-*-
# BaseReader is a Abstract class, therefore it cannot be instantiation
from abc import ABCMeta, abstractmethod

from pathlib import Path



class BaseReader(metaclass=ABCMeta):
    def __init__(self, fileStyle, file_path):
        """
        we get self.files from file_path by filtering all the fileStyle
        :param fileStyle: list, such as ['jpg','png','jpeg']
        :param file_path:
        """
        assert isinstance(fileStyle, list), f"{fileStyle} should be a list"
        if Path(file_path).is_dir():    # filter all endswith all fileStyle by recursion
            self.files = [str(file) for suffix in fileStyle for file in list(Path(file_path).rglob(f'*.{suffix}'))]
        elif Path(file_path).is_file():
            self.files = [file_path]
            self.file_pth = file_path       # only one file
        else:
            raise Exception(f"@Moss-Error: {file_path} does not exist")

        self.nf = len(self.files)
        assert self.nf > 0, f"No file suit for {fileStyle} in {file_path}"


    @abstractmethod
    def file_Reader(self, file):
        """
        According the fileStyle to Repeat the dataReader and pre-procession
        :param file: (str)
        :return:
        """
        pass
# -*-coding:utf-8-*-
"""
created by @Moss 2023/08/07
数据2.5质量评估   --全面性
Abstract：
    读取数据更新记录表，统计(1)各个学校在该项目下所有学校的数量占比；
                         (2)各个学校下的当年各个月份的数据量占比；
Usage：
    python
"""
import json
import re
import time
from tqdm import tqdm

import numpy as np
import xml.etree.ElementTree as ET
from collections import Counter
from datetime import datetime
from pathlib import Path


from pylts.utils.statistics import init_logger



class imgDirs_Reader:
    def __init__(self, proj_dirs, img_format, lab_formats):
        self.lab_formats = lab_formats
        self.img_format = img_format
        assert isinstance(proj_dirs, list), f"{proj_dirs} should be a list"
        self.proj_dirs = proj_dirs

        self.nproj = len(self.proj_dirs)
        assert self.nproj > 0, f"No dir in {self.proj_dirs}"

        self.logger = init_logger(log_level='INFO')  # logging.INFO

    def __next__(self):
        """
        :return:
        target,
        self.rec_time,
        proj_dir
        """
        if self.count == self.nproj:
            raise StopIteration
        proj_dir = self.proj_dirs[self.count]
        if not Path(proj_dir).is_dir() and Path(proj_dir).exists():
            raise TypeError(f"{proj_dir} is not a dir")

        self.logger.info(f"the {self.count + 1}th proj: {proj_dir}; ")

        # read and processing
        files, format = self.read_proj_dir(proj_dir, data_formats=self.lab_formats)        # '.xml','.jpg.json','.json'
        self.nf, self.format = len(files), format

        if format == self.lab_formats[0]:           # 'xml'
            target = self.detect_dataset_coverage(files_xml=files)  # sub_main for detect
        elif format == self.lab_formats[1]:         # '.jpg.json'
            target = self.pose_dataset_coverage(files_pose=files)      # sub_main for pose_det
        elif format == self.lab_formats[2]:         # '.json'
            target = self.segment__dataset_coverage(files_seg=files)           # 分割
        elif format == self.img_format:             # '.jpg'
            target = self.classify_dataset_coverage(files_jpg=files)  # sub_main for classify special(all proj have)
        else:
            raise RuntimeError

        self.count += 1
        return target, self.rec_time, proj_dir


    def read_proj_dir(self, proj_dir, data_formats:list) ->(list,str):
        def find_suffix(proj_dir, data_formats):
            for file in list(Path(proj_dir).rglob('*.*')):
                for format in data_formats:
                    if format in str(file):
                        return format
            return '.jpg'

        suffix = find_suffix(proj_dir, data_formats)
        files = [self.param_path(file) for file in tqdm(list(Path(proj_dir).rglob(f'*{suffix}')))]

        return files, suffix


    def segment__dataset_coverage(self, files_seg):
        """
        分割项目-数据集全面性指标，统计学校，数量，季度，json标签信息
        :return: np.shape(n, 4)
        """
        file_matrix = np.vstack(files_seg)

        unique_scl, unique_season, unique_scene, unique_day = \
            np.unique(file_matrix[:, 0]), np.unique(file_matrix[:, 1]), np.unique(file_matrix[:, 3]),np.unique(file_matrix[:, 4])

        scl_season, scl_num, scl_scene, scl_day, scl_lab = [{} for _ in range(5)]
        for scl in unique_scl:
            scl_num[scl] = 0
            scl_scene[scl], scl_season[scl],  scl_day[scl] = [{} for _ in range(3)]
            for season in unique_season:
                scl_season[scl][season] = np.sum((file_matrix[:, 0] == scl) & (file_matrix[:, 1] == season))
                scl_num[scl] += scl_season[scl][season]
            for scene in unique_scene:
                scl_scene[scl][scene] = np.sum((file_matrix[:, 0] == scl) & (file_matrix[:, 3] == scene))
            for day in unique_day:
                scl_day[scl][day] = np.sum((file_matrix[:, 0] == scl) & (file_matrix[:, 4] == day))

        # 检测项目，单独更新scl_lab
        for i, row in enumerate(file_matrix[:, 2]):
            key = file_matrix[i, 0]
            if key not in scl_lab:
                scl_lab[key] = {}
            for k, v in row.items():
                if k not in scl_lab[key]:
                    scl_lab[key][k] = 0
                scl_lab[key][k] += v

        target = self.target_normal(scl_num, scl_season, scl_lab, scl_scene, scl_day)

        return target


    def classify_dataset_coverage(self, files_jpg):
        """
        分类项目-数据集全面性指标，统计学校，数量，季度，标签信息
        :return: np.shape(n, 4)
        """
        file_matrix = np.vstack(files_jpg)

        unique_scl, unique_season, unique_lab, unique_scene, unique_day = [np.unique(file_matrix[:, i]) for i in range(5)]
        scl_num, scl_season, scl_lab, scl_scene, scl_day = [{} for _ in range(5)]
        for scl in unique_scl:
            scl_num[scl] = 0
            scl_scene[scl], scl_season[scl], scl_lab[scl],  scl_day[scl] = [{} for _ in range(4)]
            for season in unique_season:
                scl_season[scl][season] = np.sum((file_matrix[:, 0] == scl) & (file_matrix[:, 1] == season))
                scl_num[scl] += scl_season[scl][season]
            for l in unique_lab:
                scl_lab[scl][l] = np.sum((file_matrix[:, 0] == scl) & (file_matrix[:, 2] == l))
            for scene in unique_scene:
                scl_scene[scl][scene] = np.sum((file_matrix[:, 0] == scl) & (file_matrix[:, 3] == scene))
            for day in unique_day:
                scl_day[scl][day] = np.sum((file_matrix[:, 0] == scl) & (file_matrix[:, 4] == day))

        # 将各项数据整合至 target
        target = self.target_normal(scl_num, scl_season, scl_lab, scl_scene, scl_day)

        return target

    def detect_dataset_coverage(self, files_xml):
        """
        检测项目-数据集全面性指标，统计学校，数量，季度，xml标签信息
        :return: np.shape(n, 4)
        """
        file_matrix = np.vstack(files_xml)

        unique_scl, unique_season, unique_scene, unique_day = \
            np.unique(file_matrix[:, 0]), np.unique(file_matrix[:, 1]), np.unique(file_matrix[:, 3]),np.unique(file_matrix[:, 4])

        scl_season, scl_num, scl_scene, scl_day, scl_lab = [{} for _ in range(5)]
        for scl in unique_scl:
            scl_num[scl] = 0
            scl_scene[scl], scl_season[scl],  scl_day[scl] = [{} for _ in range(3)]
            for season in unique_season:
                scl_season[scl][season] = np.sum((file_matrix[:, 0] == scl) & (file_matrix[:, 1] == season))
                scl_num[scl] += scl_season[scl][season]
            for scene in unique_scene:
                scl_scene[scl][scene] = np.sum((file_matrix[:, 0] == scl) & (file_matrix[:, 3] == scene))
            for day in unique_day:
                scl_day[scl][day] = np.sum((file_matrix[:, 0] == scl) & (file_matrix[:, 4] == day))

        # 检测项目，单独更新scl_lab
        for i, row in enumerate(file_matrix[:, 2]):
            key = file_matrix[i, 0]
            if key not in scl_lab:
                scl_lab[key] = {}
            for k, v in row.items():
                if k not in scl_lab[key]:
                    scl_lab[key][k] = 0
                scl_lab[key][k] += v

        target = self.target_normal(scl_num, scl_season, scl_lab, scl_scene, scl_day)

        return target

    def pose_dataset_coverage(self, files_pose):
        """
        姿态项目-数据集全面性指标，统计学校，数量，季度，xml标签信息
        :return: np.shape(n, 4)
        """
        file_matrix = np.vstack(files_pose)

        unique_scl, unique_season, unique_lab, unique_scene, unique_day = [np.unique(file_matrix[:, i]) for i in range(5)]

        scl_season, scl_num, scl_lab, scl_scene, scl_day = [{} for _ in range(5)]
        for scl in unique_scl:
            scl_num[scl] = 0
            scl_scene[scl], scl_season[scl], scl_lab[scl], scl_day[scl] = [{} for _ in range(4)]
            for season in unique_season:
                scl_season[scl][season] = np.sum((file_matrix[:, 0] == scl) & (file_matrix[:, 1] == season))
                scl_num[scl] += scl_season[scl][season]
            for l in unique_lab:
                scl_lab[scl][l] = np.sum((file_matrix[:, 0] == scl) & (file_matrix[:, 2] == l))
            for scene in unique_scene:
                scl_scene[scl][scene] = np.sum((file_matrix[:, 0] == scl) & (file_matrix[:, 3] == scene))
            for day in unique_day:
                scl_day[scl][day] = np.sum((file_matrix[:, 0] == scl) & (file_matrix[:, 4] == day))

        target = self.target_normal(scl_num, scl_season, scl_lab, scl_scene, scl_day)

        return target

    def target_normal(self, scl_num, scl_season, scl_lab, scl_scene, scl_day):
        """
        将各项统计数据(归一化)并合并至np矩阵
        :return: np.shape(len(scl_num), 7)
        """
        scls_season_lab = np.zeros((len(scl_num), 7), dtype=object)
        for (i, (scl, num)), (_, seasons), (_, labs), (_, scene), (_, days) in \
                zip(enumerate(sorted(scl_num.items())), sorted(scl_season.items()), sorted(scl_lab.items()), sorted(scl_scene.items()), sorted(scl_day.items())):
            scls_season_lab[i] = scl, num, np.round(num / self.nf, decimals=4), self.param_dict(seasons), self.param_dict(labs), \
                                 self.param_dict(scene), self.param_dict(days)

        # 数量排序
        scls_season_lab = scls_season_lab[np.argsort(scls_season_lab[:, 1])]

        return scls_season_lab

    def param_path(self, file):
        """
        # assert isinstance(file, Path)
        pandas read the excel file and get TableHead Info
        :return:
        """
        # post-processing
        school = file.parents[1].stem
        season = file.parent.stem[0]                                # 季度 A,B,C,D
        scene = self.etract_odd_lab(string=file.parent.stem)        # 场景(特殊标签)
        time = self.etract_update_time(string=file.parent.stem)

        if self.lab_formats[0] in file.name:
            lab = self.read_xml(file)                  # 检测读取xml的label name
        elif self.lab_formats[1] in file.name:      # .jpg.json
            lab = 'person'                          # 人体姿态检测标签，仅有person类
        elif self.lab_formats[2] in file.name:      # .json
            lab = self.read_json(file)               # 分割项目 获取标签
        elif '.jpg' in file.name:
            lab = file.parents[2].stem  # 分类label name
        else:
            raise ValueError(f'@Moss: formats not suit for all situations')


        return np.array([school, season, lab, scene, time])

    @staticmethod
    def read_json(file: Path) ->dict:
        with open(file, 'r') as f:
            labels = [data['label'] for data in json.load(f)['shapes']]

        label_counts = dict(Counter(labels))
        time_lab = file.parent.stem[0:6] + '-'

        return {time_lab + key: val for key, val in label_counts.items()}


    @staticmethod
    def read_xml(file: Path) -> dict:
        """For detect proj get det_lab_name from xml"""
        anno = ET.parse(file).getroot()
        names = [obj.find('name').text for obj in anno.findall('object')]

        name_counts = dict(Counter(names))
        time_lab = file.parent.stem[0:6] + '-'

        return {time_lab + key:val for key,val in name_counts.items()}

    @staticmethod
    def etract_odd_lab(string: str) -> str:
        pattern = r'.*_.*_.*_(.*)_(\d+)$'
        match =re.match(pattern, string)
        if match:
            return match.group(1)
        else:
            return 'Norm'

    @staticmethod
    def etract_update_time(string: str) -> str:
        """判断数据所属的时间范围"""
        sample_time = string[2:6] + string[7:11]            # e.g. 2023 + 0602
        sample_time = datetime.strptime(sample_time, '%Y%m%d')
        days = (datetime.now() - sample_time).days
        if days <= 180:     # 6个月内的数据统计
            return f"Within_{min(180, (days-1)//30 * 30 + 30)}"     # 每30天间隔1次，若整个项目中没有这样的数据，则不会生成该key
        else:
            return 'Morethan_180'

    @staticmethod
    def param_dict(file_dict: dict) -> dict:
        """数值转百分比"""
        total_num = sum(file_dict.values())
        for key in file_dict:
            file_dict[key] = np.round(file_dict[key] / total_num, decimals=4)

        # 增加合并特殊标签
        if 'head_middle' in file_dict.keys():
            _file_dict = file_dict.copy()
            merge_style = ['head_middle', 'head_up']
            file_dict = {'head_up': sum(_file_dict[k] for k in merge_style if k in _file_dict)}
            file_dict.update({k:v for k,v in _file_dict.items() if k not in merge_style})

        return file_dict

    @property
    def rec_time(self) ->str:
        """Now time"""
        time_tuple = time.localtime(time.time())
        timestamp = time.strftime("%Y%m%d %H:%M", time_tuple)       # such '20230814 10:20'

        return timestamp

    def __iter__(self):
        self.count = 0
        return self

    def __len__(self):
        return self.nproj




if __name__ == '__main__':

    with open('readpath.txt', 'r') as f:
        data_path = f.read().splitlines()


    datasets = imgDirs_Reader(proj_dirs=data_path, img_format='.jpg',lab_formats=['.xml','.jpg.json','.json'])     # 顺序不能变[检测,姿态，分割，分类]


    data_Info = np.zeros((len(data_path), 1), dtype=object)
    for i, (target, timestamp, proj_dir) in enumerate(datasets):
        target_datasets = {'Rec_Time': timestamp, 'target': target, 'Num_target': datasets.nf,
                           'path': proj_dir,
                           'sample_type': datasets.format}
        data_Info[i] = target_datasets
        del target_datasets

    np.save('data_Info.npy',data_Info)



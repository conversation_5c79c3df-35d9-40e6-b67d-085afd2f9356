# -*-coding:utf-8-*-
import json
from pathlib import Path



class data_reader:
    def __init__(self, data_path):
        if Path(data_path).is_dir():
            pass
        elif Path(data_path).is_file():
            self.data_path = data_path
        else:
            raise TypeError

    def json_read(self):
        # 判断文件后缀is json
        if not Path(self.data_path).suffix == '.json':
            raise ValueError
        with open(self.data_path, 'r') as f:
            data = json.load(f)

        with open('new_data.json', 'w') as fw:
            json.dump(data, fw, indent=4)       # 缩进４



if __name__ == '__main__':
    path = '/root/share175/sport_datas/rope_jump/human_pose_rope/train/images/jiupingtai/Maanshan_hongxing_2023_3_3_16_36_30_192.168.2.206_1_1_A_face_unknow_unknow_20230316160515_1.json'
    data_reader(data_path=path).json_read()
# -*-coding:utf-8-*-
"""
背景：labelMe、labelImg 是进行图像中的目标检测和分类任务提供的终端；该脚本为视频对应的图像标签进行分类
* 加速 图像标签的人工打标签速度 【自动化打开文件、数据】

我要在文件夹A中[文件夹中有很多子文件夹A1...An， 每个子文件夹中有若干个mp4文件， pkl文件和jpg文件]，频繁的做下面的操作：
1. 逐个打开Ai子文件夹，逐个打开子文件夹Ai中的jpg图片；
2. 对每个jpg文件打标签：例如，我认为该jpg图片为1,则将jpg文件和同名的pkl文件放到1文件夹中；
                            认为该jpg图片为0, 则删除该jpg文件和同名的pkl文件；

上述操作，主要耗时在 打开文件夹和图片文件，以及移动文件的操作； 自动化脚本执行后，只需要(键盘)输入对应标签数字；
* Example：
脚本执行后，会依次在ubuntu18.04的图形界面打开Ai文件夹，逐个打开jpg文件，
等待我的标签输入，例如我输入1,则将该jpg文件和同名的pkl文件移动到1文件夹，
然后继续打开下一个jpg文件，直到该Ai文件夹中的所有jpg文件都被我全部执行完成，则继续打开Ai+1文件夹，
依次循环；

* 测试环境： Linux-ubuntu18.04
* 依赖库：sudo apt-get install wmctrl, sudo apt-get install xdotool
* 依赖环境[可选]: pip install pynput
            用于 监控键盘按键, 获取标签

打开图像方式：viewer_process = subprocess.Popen(['eog', str(jpg_path)])     # xdg-open 能打开窗口，但不好关闭
关闭图像窗口方式：os.system(f'kill {viewer_process.pid}')
打开文件夹窗口方式：xdg-open
关闭文件夹窗口方式：
window_ids = subprocess.check_output(['xdotool', 'search', '--name', Path(folder_path).name]).decode().strip()
subprocess.run(['wmctrl', '-i', '-c', window_ids.split()[0]])

"""
import os
import shutil
import subprocess
from pathlib import Path

from pynput import keyboard
import time

import platform


class ImageLabeler:
    def __init__(self, folder_path):
        self.folder_path = Path(folder_path)
        self.current_folder = None
        self.current_viewer_process = None

    def listen_for_key(self, target_keys: set):
        pressed_key = {'value': None}

        def on_press(key):
            try:
                if key.char in target_keys:
                    pressed_key['value'] = key.char
            except AttributeError:
                pass

        listener = keyboard.Listener(on_press=on_press)
        listener.start()

        while pressed_key['value'] not in target_keys:
            time.sleep(0.1)

        listener.stop()

        return pressed_key['value']

    def open_folder(self, folder_path):
        raise NotImplementedError("This method must be implemented by subclasses")

    def close_folder(self, folder_path):
        raise NotImplementedError("This method must be implemented by subclasses")

    def open_image(self, image_path):
        raise NotImplementedError("This method must be implemented by subclasses")

    def close_image(self):
        raise NotImplementedError("This method must be implemented by subclasses")

    def process_folder(self):
        for sub_folder in self.folder_path.iterdir():
            if sub_folder.is_dir():
                self.process_sub_folder(sub_folder)

    def process_sub_folder(self, sub_folder_path):
        jpg_files = list(sub_folder_path.glob('*.jpg'))
        if not jpg_files:
            return

        self.open_folder(str(sub_folder_path))
        self.current_folder = sub_folder_path

        for jpg_file in jpg_files:
            jpg_path = jpg_file
            pkl_file = jpg_path.with_suffix('.pkl')

            self.open_image(str(jpg_path))
            label = self.listen_for_key({'0', '1'})

            self.close_image()

            if label == '1':
                dest_folder = sub_folder_path / '1'
                dest_folder.mkdir(exist_ok=True)
                shutil.move(str(jpg_path), str(dest_folder / jpg_path.name))
                if pkl_file.exists():
                    shutil.move(str(pkl_file), str(dest_folder / pkl_file.name))
                print(f"已将 {jpg_path.name} 和 {pkl_file.name} 移动到 {dest_folder}")
            elif label == '0':
                jpg_path.unlink()
                if pkl_file.exists():
                    pkl_file.unlink()
                print(f"已删除 {jpg_path.name} 和 {pkl_file.name}")
            else:
                print("无效的输入，跳过该文件")

        self.close_folder(str(sub_folder_path))
        self.current_folder = None


class LinuxImageLabeler(ImageLabeler):
    def open_folder(self, folder_path):
        try:
            subprocess.Popen(['xdg-open', folder_path])
        except Exception as e:
            print(f"无法打开文件夹 {folder_path}: {e}")

    def close_folder(self, folder_path):
        folder_name = Path(folder_path).name
        try:
            window_ids = subprocess.check_output(['xdotool', 'search', '--name', folder_name]).decode().strip()
            if window_ids:
                subprocess.run(['wmctrl', '-i', '-c', window_ids.split()[0]])
            else:
                print(f"未找到文件夹窗口: {folder_path}")
        except Exception as e:
            print(f"无法关闭文件夹窗口: {e}")

    def open_image(self, image_path):
        try:
            return subprocess.Popen(['eog', image_path])
        except Exception as e:
            print(f"无法打开图片 {image_path}: {e}")
            return None

    def close_image(self):
        if self.current_viewer_process and self.current_viewer_process.poll() is None:
            try:
                os.system(f'kill {self.current_viewer_process.pid}')
            except Exception as e:
                print(f"无法关闭图片查看器: {e}")
        self.current_viewer_process = None


class WindowsImageLabeler(ImageLabeler):
    def open_folder(self, folder_path):
        try:
            os.startfile(folder_path)
        except Exception as e:
            print(f"无法打开文件夹 {folder_path}: {e}")

    def close_folder(self, folder_path):
        folder_name = Path(folder_path).name
        try:
            subprocess.run(['taskkill', '/F', '/FI', f'TITLE={folder_name}'], shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        except Exception as e:
            print(f"无法关闭文件夹窗口: {e}")

    def open_image(self, image_path):
        try:
            os.startfile(image_path)
            # 在 Windows 上，我们无法直接获取图片查看器的进程 ID，因此这里返回 None
            return None
        except Exception as e:
            print(f"无法打开图片 {image_path}: {e}")
            return None

    def close_image(self):
        # 在 Windows 上，我们无法直接关闭图片查看器，因此这里不执行任何操作
        pass


if __name__ == "__main__":
    folder_a_path = "/path/to/folder/A"  # 替换为你的文件夹 A 的实际路径

    if platform.system() == "Windows":
        labeler = WindowsImageLabeler(folder_a_path)
    else:
        labeler = LinuxImageLabeler(folder_a_path)

    labeler.process_folder()
    print("所有文件处理完成！")
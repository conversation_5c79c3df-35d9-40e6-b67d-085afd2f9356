# Multi-Task Series　Torch　Pattern Library

Info: 2023-2025

1. 基于数据加载的各种方式，搭建一种基于numpy或panda的数据工厂模型；
2. 基于各种算法的微调应用, 搭建一种基于Torch框架的多任务模型插件库，
   包括：导入包(设置环境，设备，资源等)，定义超参数，定义模型，定义数据载入，定义优化器，损失，实例化模型等，训练验证模板，推理绘图等，导出onnx

#### 组织架构

* 配置工具|环境
  * [x] 测试python环境可用：                                                         Configs/Check_ENVs.py
  * [?] 终端中的富文本和美观的格式:                                                    Configs/console.py

  * [ ] yaml(替代argparse): 凡是配置模型、路径、参数等信息，一律采用yaml读取配置的方式 在脚本中
  * [?] 将一些重复使用的预处理，例如transforms.Compose()  写入到*.py 配置文件中呢
  * [x] 字典转换成可调用类, 支持python/json/yml文件的参数调用：                                 pylts/config/ConfigDict.py
  * [x] 多卡训练时选择GPU:                                                                   pylts/config/devices_selected.py
  * [x] 分布式训练初始化[多卡训练]:                                                           pylts/config/distributed_init.py[class DDP_Trainer()]
  * [x] 注册器模块: 允许通过字符串映射和实例化模块。                                             pylts/config/registry.py
  * [x] 回调机制 CallbackHooks                                                               pylts/config/callbacks.py
* 数据
  * [ ] 数据加载，划分数据集
    * [ ] 文件加速读取 Path().rglob
  * [x] 加速check图像标签的自动化流程[ubuntu18.04]                                            data_provider/check_Lab_speedup/LabelClassifyImg.py
  * [?] *.rec 的数据存储和加载节省空间，在 PyTorch 中读取 .rec文件替代lmdb
  * [x] 多线程/进程 并行工具包 多线程并行io操作,多进程CPU密集操作                                pylts/datasets/multiParallel_toolkit.py
  * [x] 图像数据可视化 存储                                                                  pylts/datasets/ImgVisualization.py
  * [ ] 数据增强，标准化预处理
  * [ ] 数据采集逻辑：                                                                       pylts/utils/methods/logics.py
* 模型
  * [ ] 基础sota模型搭建                                                                    model_zoo/:
    * [x] 分类模型 efficientNetV2
    * [x] 分割模型 cv2.grabCut
    * [ ] 权重初始化
    * [x] 预训练模型下载                                                                                 timm_download.py
  * [ ] 模型导出onnx, 推理(use pt/onnx/trt) tensorRT模型加载与推理                            models/model_tensorRT.py
  * [-] 量化感知训练(QAT)
    * [x] 量化环境部署                                                                      models/QATEnv_check.py
    * [?] 量化训练
  
* 损失函数与优化器
  * [x] loss(for分类模型)                                                           pylts/utils/loss/select_loss.py
  * [x] 优化器调用                                                                  pylts/utils/optimizer/select_optim.py
    * [x] 调度器选择 scheduler                                                       pylts/utils/optimizer/scheduler.py
  * [ ] 部分参数冻结                                                                             freeze_module.py
  * [ ] 断点续传                                                                                 resume.py
  * [ ] 分布式训练, 验证
  * [?] 量化训练流程      models/model_qat.py
  * [ ] 训练策略, 如梯度裁剪、冻结权重训练
  * [ ] 分类/检测模型 的评价指标:                                                             estimate/indicator.py
* 监控模型及可视化
  * [ ] TensorBoardX
  * [x] GPU显存监控：在资源空闲时主动占用[排队模式]————tools/GPU_Monitor.sh
* 工具箱：utils/statistic/
  * [x] 日志模块 Mod_logger.py
  * [x] 时间模块 Mod_timer.py
  * [x] 调试模块 Mod_debug.py   包含模块：TryExcept ImageSaver
  * [x] 缓存模块 Np_cache.py    临时文件缓存 替换[np,np...]在内存中导致的溢出  

其他相关内容待更新：

1. 计算指标的基础配置 pylts/utils/metric/baseComputes.py
2. main.py中尝试加入debug模式，在opt.debug默认=False
3. 差1个主函数，加入以上所有可以默认载入的模块，如debug模块可默认关闭载入

#### 数据质量管理平台

做1个数据质量管理平台，通过在平台上操作各个设定好的模块，实现数据的高效筛选和管理；
数据主要包括: 图片、视频、pkl文件等(例如对于1个图片数据集，主要数据就是jpg或png图片)
平台前端: 以本地网页端的形式展示，可以执行一定的操作(例如点击等键盘鼠标操作)
后端功能包括：【后端是我本地电脑的ubuntu系统】

1. 能够使用指定的python脚本，执行相关操作：
例如: 前端点击可以新增1个子模块，在子模块中点击或输入：点击则弹窗中找到我提供的python脚本路径(或能直接粘贴输入脚本的绝对路径)，
假设加载的这个python脚本假设是1个随机抽取jpg图片的脚本，前端的子模块将需要提供输入数据路径、输出数据路径，需要抽取的图片数量
(在终端中执行的效果是 python my_random_pic.py --input "./xxx/" --output "./xxx/out" --img_num 10)
然后需要1个可以点击的执行按钮，点击后子模块在终端指定的虚拟环1境(conda activate env1)中执行并能看到打印的内容
执行完成后，能够提供1个按钮(output)，便于我一键打开
总的来说，对于这样一个能够执行python脚本的模块，只要该脚本提供多少个路径输入选项(在上面例子中是 input和output)，就需要在前端显示多少个路径按钮，便于输入和打开；
2. 人工筛选数据的子模块：


Zotero 用户名: Moss2
Zotero User id : 17677788
密钥：H2CZec3QMQORkRI4ASL272KF

gmail
应用名称：arXiv GitHubActions Email
应用专用密码：mujo rzmu mvpf qjth


我需要你帮助我解决一个GitHub Actions工作流程的问题。

**项目背景：**
- 原项目：https://github.com/TideDra/zotero-arxiv-daily
- 我的fork版本：https://github.com/PYLuck/zotero-arxiv-daily
- 我是视觉算法领域的科研工作者，使用Ubuntu 22.04环境
- 我已经fork了项目并完成了基本配置

**问题描述：**
当我在GitHub上执行"Test workflow -> Run workflow"流程时，测试过程中出现了错误。

**具体要求：**
1. 请使用Edge浏览器工具访问错误日志页面：https://github.com/PYLuck/zotero-arxiv-daily/actions/runs/16713807872/job/47303423251
2. 分析具体的错误信息和失败原因
3. 如果需要，也可以访问我的项目主页：https://github.com/PYLuck/zotero-arxiv-daily 来了解项目配置
4. 提供详细的解决方案，包括需要修改的文件、配置或代码，在我的github上修改
5. 不要修改和查看任何本地代码，因为这个项目我并没有部署到本地测试，而是在github上直接测试：一切代码均以-
原项目：https://github.com/TideDra/zotero-arxiv-daily
我的fork版本：https://github.com/PYLuck/zotero-arxiv-daily
为参考
6. 如果你需要登陆才能查看错误，使用如下登陆用户名和密码：

**期望输出：**
- 错误原因的详细分析
- 具体的修复步骤
- 如果需要修改代码或配置文件，请直接帮我完成修改，如果涉及密码相关，告诉我让我手动完成



### 基础库说明

* 核心库:
  * torch (PyTorch): 核心的深度学习框架。
  
  * numpy: 用于进行数值计算。
  
  * cv2 (OpenCV): 用于图像和视频处理。
  
  * PIL (Pillow): 用于图像操作。
  
  * 机器学习与深度学习:
    * timm (PyTorch Image Models): 用于访问各种预训练的图像模型。
    * torchvision: 作为PyTorch生态的一部分，提供计算机视觉相关的工具和数据集。
    * tensorrt: 用于优化NVIDIA GPU上的深度学习模型。
    * pycuda: 用于在Python中进行CUDA编程，与GPU交互。
    * onnx: 用于实现不同深度学习框架之间的模型转换和互操作。
    * onnxruntime: 用于高效运行ONNX格式的模型。
    * nncf (Neural Network Compression Framework): 用于神经网络的压缩和量化。
    * openvino: 用于在英特尔硬件上优化和部署深度学习模型。
    * sklearn (scikit-learn): 提供常用的机器学习工具。
    * transformers: 用于自然语言处理任务（可能用于特定的模型）。
    * kornia: 一个可微分的计算机视觉库。
  
  * 数据与配置:
    * yaml (PyYAML): 用于读取和解析YAML格式的配置文件。
    * pandas: 用于数据处理和分析，尤其擅长处理Excel文件。
    * json: 用于处理JSON格式的数据。
    * easydict: 允许像访问对象属性一样访问字典的键。
  
  * 工具及其他:
    * tqdm: 用于在循环中显示进度条。
    * matplotlib: 用于数据可视化和绘图。
    * ffmpeg: 用于处理视频文件。
    * pynput: 用于监控和控制键盘输入。
    * rich: 用于在终端中创建富文本和精美的格式化输出。
    * colorlog: 用于输出带有颜色的日志信息。
    
    
    
    
    
    
    
    # 
    
    # 其它
    
    你是Claude 4.0，集成在Cursor IDE中，遵循核心工作流（研究 -> 构思 -> 规划 -> 执行 -> 评审）用中文协助用户，面向专业程序员，交互应简洁专业，避免不必要解释。
    你必须在每个响应的开头用方括号声明你当前的模式(例如  `[模式：X]` )。没有例外。
    [沟通守则]
    
    1.  响应以模式标签 `[模式：X]` 开始，初始为 `[模式：研究]`。
    2.  核心工作流严格按 `研究 -> 构思 -> 计划 -> 执行 -> 评审` 顺序流转，用户可指令跳转。
    
    [核心工作流详解]
    
    1. `[模式：研究]`：理解需求。
    
        目的：信息收集和深入理解
    
        核心思维应用：
    
        - 系统地分解技术组件
        - 清晰地映射已知/未知元素
        - 考虑更广泛的架构影响
        - 识别关键技术约束和要求
    
        允许：
    
        - 阅读文件
        - 提出澄清问题
        - 理解代码结构
        - 分析系统架构
        - 识别技术债务或约束
        - 创建任务文件（参见下面的任务文件模板）
        - 创建功能分支
    
        禁止：
    
        - 建议
        - 实施
        - 规划
        - 任何行动或解决方案的暗示
    
        研究协议步骤：
    
        1. 创建任务文件（如需要）：
    
            ```shell
            mkdir -p .tasks && touch ".tasks/${TASK_FILE_NAME}_[TASK_IDENTIFIER].md"
            ```
    
        2. 分析与任务相关的代码：
    
            - 识别核心文件/功能
            - 追踪代码流程
            - 记录发现以供以后使用
    
        输出格式：
        以 [模式：研究] 开始，然后只有观察和问题。
        使用markdown语法格式化答案。
        除非明确要求，否则避免使用项目符号。
    
        持续时间：直到明确信号转移到下一个模式
    
    2. `[模式：构思]`：
    
        目的：头脑风暴潜在方法
    
        核心思维应用：
    
        - 运用辩证思维探索多种解决路径
        - 应用创新思维打破常规模式
        - 平衡理论优雅与实际实现
        - 考虑技术可行性、可维护性和可扩展性
    
        允许：
    
        - 讨论多种解决方案想法
        - 评估优势/劣势
        - 寻求方法反馈
        - 探索架构替代方案
        - 在"提议的解决方案"部分记录发现
    
        禁止：
    
        - 具体规划
        - 实施细节
        - 任何代码编写
        - 承诺特定解决方案
    
        创新协议步骤：
    
        1. 基于研究分析创建计划：
            - 研究依赖关系
            - 考虑多种实施方法
            - 评估每种方法的优缺点
            - 添加到任务文件的"提议的解决方案"部分
        2. 尚未进行代码更改
    
        输出格式：
        以[模式：构思]开始，然后只有可能性和考虑因素。
        以自然流畅的段落呈现想法。
        保持不同解决方案元素之间的有机联系。
    
        持续时间：直到明确信号转移到下一个模式
    
    3. `[模式：规划]`：
    
        目的：创建详尽的技术规范
    
        核心思维应用：
    
        - 应用系统思维确保全面的解决方案架构
        - 使用批判性思维评估和优化计划
        - 制定全面的技术规范
        - 确保目标聚焦，将所有规划与原始需求相连接
    
        允许：
    
        - 带有精确文件路径的详细计划
        - 精确的函数名称和签名
        - 具体的更改规范
        - 完整的架构概述
    
        禁止：
    
        - 任何实施或代码编写
        - 甚至可能被实施的"示例代码"
        - 跳过或缩略规范
    
        规划协议步骤：
    
        1. 查看"任务进度"历史（如果存在）
    
        2. 详细规划下一步更改
    
        3. 提交批准，附带明确理由：
    
            ```
            [更改计划]
            - 文件：[已更改文件]
            - 理由：[解释]
            ```
    
            
    
        必需的规划元素：
    
        - 文件路径和组件关系
        - 函数/类修改及签名
        - 数据结构更改
        - 错误处理策略
        - 完整的依赖管理
        - 测试方法
    
        强制性最终步骤：
        将整个计划转换为编号的、顺序的清单，每个原子操作作为单独的项目
    
        清单格式：
    
        ```
        实施清单：
        1. [具体行动1]
        2. [具体行动2]
        ...
        n. [最终行动]
        ```
    
        输出格式：
    
        以[模式：规划]开始，然后只有规范和实施细节。
        使用markdown语法格式化答案。
    
        持续时间：直到计划被明确批准并信号转移到下一个模式
    
    4. `[模式：执行]`：
    
        目的：准确实施模式3中规划的内容
    
        核心思维应用：
    
        - 专注于规范的准确实施
        - 在实施过程中应用系统验证
        - 保持对计划的精确遵循
        - 实施完整功能，具备适当的错误处理
    
        允许：
    
        - 只实施已批准计划中明确详述的内容
        - 完全按照编号清单进行
        - 标记已完成的清单项目
        - 实施后更新"任务进度"部分（这是执行过程的标准部分，被视为计划的内置步骤）
    
        禁止：
    
        - 任何偏离计划的行为
        - 计划中未指定的改进
        - 创造性添加或"更好的想法"
        - 跳过或缩略代码部分
    
        执行协议步骤：
    
        1. 完全按照计划实施更改，如生成python代码默认符合PEP8编码规范
    
        2. 每次实施后追加到"任务进度"（作为计划执行的标准步骤）：
    
            ```
            [日期时间]
            - 已修改：[文件和代码更改列表]
            - 更改：[更改的摘要]
            - 原因：[更改的原因]
            - 阻碍因素：[阻止此更新成功的阻碍因素列表]
            - 状态：[未确认|成功|不成功]
            ```
    
            
    
        3. 要求用户确认：“状态：成功/不成功？”
    
        4. 如果不成功：返回 [模式：规划]
    
        5. 如果成功且需要更多更改：继续下一项
    
        6. 如果所有实施完成：移至 [模式：审查]
    
        代码质量标准：
    
        - 始终显示完整代码上下文
        - 在代码块中指定语言和路径
        - 适当的错误处理
        - 标准化命名约定
        - 清晰简洁的注释
    
        偏差处理：
        如果发现任何需要偏离的问题，立即返回 [模式：规划]
    
        输出格式：
        以[模式：执行]开始，然后只有与计划匹配的实施。
        包括正在完成的清单项目。
    
        进入要求：在有明确的用户批准执行命令后，才能进入
    
        
    
    5. `[模式：评审]`：
    
        目的：无情地验证实施与计划的符合程度
    
        核心思维应用：
    
        - 应用批判性思维验证实施准确性
        - 使用系统思维评估整个系统影响
        - 检查意外后果
        - 验证技术正确性和完整性
    
        允许：
    
        - 逐行比较计划和实施
        - 已实施代码的技术验证
        - 检查错误、缺陷或意外行为
        - 针对原始需求的验证
        - 最终提交准备
    
        必需：
    
        - 明确标记任何偏差，无论多么微小
        - 验证所有清单项目是否正确完成
        - 检查安全影响
        - 确认代码可维护性
    
        审查协议步骤：
    
        1. 根据计划验证所有实施
        2. 完成任务文件中的"最终审查"部分
    
        偏差格式：
        `检测到偏差：[偏差的确切描述]`
    
        报告：
        必须报告实施是否与计划完全一致
    
        结论格式：
        `实施与计划完全匹配` 或 `实施偏离计划`
    
        输出格式：
        以[模式：评审]开始，然后是系统比较和明确判断。
        使用markdown语法格式化。
    
    ### 关键协议指南
    
    - 未经明确许可，你不能在模式之间转换
    - 你必须在每个响应的开头声明你当前的模式
    - 在执行模式中，你必须100%忠实地遵循计划
    - 在评审模式中，你必须标记即使是最小的偏差
    - 在你声明的模式之外，你没有独立决策的权限
    - 你必须将分析深度与问题重要性相匹配
    - 你必须与原始需求保持清晰联系
    - 除非特别要求，否则你必须禁用表情符号输出
    - 如果没有明确的模式转换信号，请保持在当前模式
    
    默认模式规则：
    
    - 除非明确指示，否则默认在每次对话开始时处于研究模式
    - 如果研究模式发现需要偏离计划，自动回到PLAN模式
    - 完成所有实施，且用户确认成功后，可以从执行模式转到评审模式
    
    ### 任务文件模板
    
    ```
    # 背景
    文件名：[TASK_FILE_NAME]
    创建于：[DATETIME]
    创建者：[USER_NAME]
    主分支：[MAIN_BRANCH]
    任务分支：[TASK_BRANCH]
    Yolo模式：[YOLO_MODE]
    
    # 任务描述
    [用户的完整任务描述]
    
    # 项目概览
    [用户输入的项目详情]
    
    ⚠️ 警告：永远不要修改此部分 ⚠️
    [此部分应包含核心RIPER-5协议规则的摘要，确保它们可以在整个执行过程中被引用]
    ⚠️ 警告：永远不要修改此部分 ⚠️
    
    # 分析
    [代码调查结果]
    
    # 提议的解决方案
    [行动计划]
    
    # 当前执行步骤："[步骤编号和名称]"
    - 例如："2. 创建任务文件"
    
    # 任务进度
    [带时间戳的变更历史]
    
    # 最终审查
    [完成后的总结]
    ```
    
    ### 占位符定义
    
    - [TASK]：用户的任务描述（例如"修复缓存错误"）
    - [TASK_IDENTIFIER]：来自[TASK]的短语（例如"fix-cache-bug"）
    - [TASK_DATE_AND_NUMBER]：日期+序列（例如2025-01-14_1）
    - [TASK_FILE_NAME]：任务文件名，格式为YYYY-MM-DD_n（其中n是当天的任务编号）
    - [MAIN_BRANCH]：默认"main"
    - [TASK_FILE]：.tasks/[TASK_FILE_NAME]_[TASK_IDENTIFIER].md
    - [DATETIME]：当前日期和时间，格式为YYYY-MM-DD_HH:MM:SS
    - [DATE]：当前日期，格式为YYYY-MM-DD
    - [TIME]：当前时间，格式为HH:MM:SS
    - [USER_NAME]：当前系统用户名
    - [COMMIT_MESSAGE]：任务进度摘要
    - [SHORT_COMMIT_MESSAGE]：缩写的提交消息
    - [CHANGED_FILES]：修改文件的空格分隔列表
    - [YOLO_MODE]：Yolo模式状态（Ask|On|Off），控制是否需要用户确认每个执行步骤
        - Ask：在每个步骤之前询问用户是否需要确认
        - On：不需要用户确认，自动执行所有步骤（高风险模式）
        - Off：默认模式，要求每个重要步骤的用户确认
    
    
    
    ### 跨平台兼容性注意事项
    
    - 上面的shell命令示例默认基于Linux环境
    - 在Windows环境中，你可能需要使用PowerShell或CMD等效命令
    - 在任何环境中，你都应该首先确认命令的可行性，并根据操作系统进行相应调整
    
    ### 性能期望
    
    - 响应延迟应尽量减少，理想情况下≤60000ms
    - 最大化计算能力和令牌限制
    - 寻求关键洞见而非表面列举
    - 追求创新思维而非习惯性重复
    - 突破认知限制，调动所有计算资源
    
    
    
    
    
    [快速模式]
    `[模式：快速]`：跳过核心工作流，快速响应。完成后用 `mcp-feedback-enhanced` 请求用户确认。
    
    [主动反馈与 MCP 服务]
    # MCP Interactive Feedback 规则
    1. 在任何流程、任务、对话进行时，无论是询问、回复、或完成阶段性任务，皆必须调用 MCP mcp-feedback-enhanced。
    2. 每当收到用户反馈，若反馈内容非空，必须再次调用 MCP mcp-feedback-enhanced，并根据反馈内容调整行为。
    3. 仅当用户明确表示「结束」或「不再需要交互」时，才可停止调用 MCP mcp-feedback-enhanced，流程才算结束。
    4. 除非收到结束指令，否则所有步骤都必须重复调用 MCP mcp-feedback-enhanced。
    5. 完成任务前，必须使用 MCP mcp-feedback-enhanced 工具向用户询问反馈。
    * **MCP 服务 **：
        * `mcp-feedback-enhanced`: 用户反馈。
        * `Context7`: 查询最新库文档 / 示例。
        * 优先使用 MCP 服务。

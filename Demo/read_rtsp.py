# -*-coding:utf-8-*-
"""
读取标定，并将标定乘以系数
用于2560、3840 转 1080p像素点
"""







rtsp = [1656,273,1575,270,1494,265,1414,261,1334,258,1253,256,1177,251]
# rtspM = [1654,361,1519,364,1381,372,1240,382,1099,381,958,387, 718815,391]



def rtsp_Mut_Rate(original_list, rate):
    new_list = [int(x * rate) for x in original_list]
    grouped_list = [new_list[i:i + 2] for i in range(0, len(new_list), 2)]

    return grouped_list


if __name__ == '__main__':
    grouped_list = rtsp_Mut_Rate(rtsp, 0.75)

    print(grouped_list)
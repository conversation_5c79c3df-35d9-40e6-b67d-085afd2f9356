import cv2
import numpy as np

# --- 步骤 1: 读取两张图片 ---
# 请将 'path/to/your/image1.jpg' 和 'path/to/your/image2.jpg' 替换为你的实际文件路径
# 为了演示，我们先创建两张符合尺寸的示例图片
# image1 将是一张纯黑色的图片
image1 = np.zeros((1080, 1920, 3), dtype=np.uint8)
# image2 将是一张纯灰色的图片
image2 = np.full((1080, 1920, 3), 128, dtype=np.uint8)

# 如果你是从文件读取，应该使用下面的代码：
# image1 = cv2.imread('path/to/your/image1.jpg')
# image2 = cv2.imread('path/to/your/image2.jpg')

# --- 步骤 2: 检查图片是否成功读取 ---
if image1 is None or image2 is None:
    print("错误：无法读取一张或多张图片，请检查文件路径。")
else:
    # 打印原始图片尺寸以供验证 (高度, 宽度, 通道数)
    print(f"第一张图片的尺寸: {image1.shape}")
    print(f"第二张图片的尺寸: {image2.shape}")

    # --- 步骤 3: 垂直拼接图片 ---
    # cv2.vconcat() 需要一个包含所有待拼接图片的列表
    # 列表中的顺序决定了拼接的顺序：[上, 下]
    stitched_image = cv2.vconcat([image1, image2])

    # --- 步骤 4: 验证并保存结果 ---
    # 打印拼接后图片的尺寸
    print(f"拼接后图片的尺寸: {stitched_image.shape}")  # 应该输出 (2160, 1920, 3)

    # 将拼接后的图片保存到磁盘
    output_path = 'stitched_image.jpg'
    cv2.imwrite(output_path, stitched_image)
    print(f"图片已成功拼接并保存到 {output_path}")

    # --- (可选) 步骤 5: 显示拼接后的图片 ---
    # 为了能调整窗口大小，添加 WINDOW_NORMAL 标志
    cv2.namedWindow('Stitched Image', cv2.WINDOW_NORMAL)
    cv2.imshow('Stitched Image', stitched_image)

    # 等待用户按键后关闭窗口
    print("按任意键关闭显示窗口...")
    cv2.waitKey(0)
    cv2.destroyAllWindows()
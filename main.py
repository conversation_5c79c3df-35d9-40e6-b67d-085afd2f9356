"""

1. 在主程序中加入debug：
parser.add_argument('--debug', type=str2bool, default=False, help='Debug mode; default false')

"""

import argparse
import torch
import threading
from pathlib import Path
from typing import List



# 测试 curosr

class OptimizedFileSearcher:
    def __init__(self, max_workers: int = 4):
        self.max_workers = max_workers
        self._lock = threading.Lock()

    def find_files_parallel(self, root_path: str, pattern: str = '*.pkl') -> List[Path]:
        """
        """
        pass


if __name__ == '__main__':

    searcher = OptimizedFileSearcher(max_workers=4)
    files = searcher.find_files_parallel(root_path='./', pattern='*.pkl')
    print(files)
